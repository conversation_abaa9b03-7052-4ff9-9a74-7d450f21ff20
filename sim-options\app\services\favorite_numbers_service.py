from datetime import datetime
from pydantic import BaseModel
from typing import Optional


class RemoveResult(BaseModel):
    success: bool = True
    responses: Optional[dict] = None


class AddBNumberResult(BaseModel):
    error_status: bool = False
    status_message: str = ""


class UpdateBNumberResult(BaseModel):
    error_status: bool = False
    status_message: str = ""


class FavoriteNumberService:
    def __init__(self, reco_client, eia_client):
        self.reco_client = reco_client
        self.eia_client = eia_client

    async def get_customer_info(self, dordaneh_msisdn: str, fields: list = None):

        eia_response = await self.eia_client.get_customer_profile(
            dordaneh_msisdn, fields
        )
        return eia_response

    async def get_list(self, phone_number, customer_type, language):
        reco_response = await self.reco_client.get_b_numbers(
            phone_number, customer_type, language
        )

        res = []
        if reco_response.status_code != 0:
            return res

        b_numbers = reco_response.b_numbers

        for b_number in b_numbers:
            res.append(
                {
                    "favorite_number": b_number["msisdn"],
                    "nick_name": b_number["nickname"],
                    "sim_type": b_number["bpartytype"],
                }
            )

        return res

    async def add_numbers(
        self, phone_number, customer_type, language, b_number, nick_name
    ):
        b_number_profile = await self.get_customer_info(b_number)
        if not b_number_profile["mtni"]:
            return AddBNumberResult(error_status=True, status_message="invalid number")

        b_number_customer_type = b_number_profile["customer_type"]
        b_number_sim_type = b_number_profile["sim_type"]

        reco_response = await self.reco_client.add_b_number(
            phone_number,
            customer_type,
            b_number,
            nick_name,
            b_number_sim_type,
            b_number_customer_type,
            language,
        )

        if reco_response.status_code != 0:
            if reco_response.status_code == 1058:
                return AddBNumberResult(
                    error_status=True, status_message="invalid nickname"
                )
            else:
                return AddBNumberResult(
                    error_status=True, status_message="unknown error"
                )

        return AddBNumberResult()

    async def remove_numbers(
        self, phone_number, customer_type, language, input_b_numbers
    ):
        b_numbers_list = await self.get_list(phone_number, customer_type, language)

        selected_b_numbers = []
        for b_number in input_b_numbers:
            if b_number_info := next(
                (
                    item
                    for item in b_numbers_list
                    if item["favorite_number"] == b_number
                ),
                None,
            ):
                selected_b_numbers.append(b_number_info)

        responses = dict()
        for b_number in selected_b_numbers:
            b_number_msisdn = b_number["favorite_number"]
            b_number_sim_type = b_number["sim_type"]
            b_number_removing = await self.reco_client.delete_b_number(
                phone_number,
                customer_type,
                language,
                b_number_msisdn,
                b_number_sim_type,
            )
            if b_number_removing.status_code != 0:
                responses[b_number_msisdn] = "Error"
            else:
                responses[b_number_msisdn] = "Success"

        if not any(status == "Success" for status in responses.values()):
            return RemoveResult(success=False, responses=responses)

        return RemoveResult(success=True, responses=responses)

    async def update_numbers(
        self, phone_number, customer_type, language, b_number, nick_name
    ):

        b_number_profile = await self.get_customer_info(b_number)
        if not b_number_profile["mtni"]:
            return UpdateBNumberResult(
                error_status=True, status_message="invalid number"
            )

        b_number_customer_type = b_number_profile["customer_type"]
        b_number_sim_type = b_number_profile["sim_type"]

        reco_response = await self.reco_client.update_b_number(
            phone_number,
            customer_type,
            language,
            b_number,
            nick_name,
            b_number_customer_type,
            b_number_sim_type,
        )

        if reco_response.status_code != 0:
            if reco_response.status_code == 1058:
                return UpdateBNumberResult(
                    error_status=True, status_message="invalid nickname"
                )
            else:
                return UpdateBNumberResult(
                    error_status=True, status_message="unknown error"
                )

        return UpdateBNumberResult()
