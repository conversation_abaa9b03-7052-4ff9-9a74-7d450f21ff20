from PIL import Image
from io import BytesIO
from reportlab.lib.pagesizes import letter
import base64


def convert_base64_to_pdf(pdf_canvas, base_file):

    image = Image.open(BytesIO(base64.b64decode(base_file)))
    image = resize_file(image_file=image)

    # Add the image to the PDF canvas
    pdf_canvas.drawInlineImage(image, 0, 0, width=image.width, height=image.height)

    # Add a new page for the next image
    pdf_canvas.showPage()

    return pdf_canvas


def resize_file(image_file):
    # Calculate the aspect ratio to fit the image within A4 size
    width, height = image_file.size
    aspect_ratio = width / height
    a4_width, a4_height = letter
    if aspect_ratio > (a4_width / a4_height):
        new_width = a4_width
        new_height = int(a4_width / aspect_ratio)
    else:
        new_height = a4_height
        new_width = int(a4_height * aspect_ratio)
    # Resize the image
    image = image_file.resize((int(new_width), int(new_height)))

    return image
