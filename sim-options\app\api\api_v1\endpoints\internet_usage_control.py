from typing import Any, Annotated

from fastapi import APIRouter
from fastapi import Depends

from core.config import authorization, eia_client

from schemas import InternetUsageControlStatus

from swagger import internet_usage_control

from services import InternetUsageService
from services.internet_usage_controle_service import UsageControlResult
from core.config import get_internet_usage_control_service

router = APIRouter()


@router.post(
    "/internet_usage_control", responses=internet_usage_control.internet_usage_control
)
async def internet_usage_control(
    internet_usage_control_status: InternetUsageControlStatus,
    internet_usage_service: Annotated[
        InternetUsageService, Depends(get_internet_usage_control_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    internet_usage_control_status = internet_usage_control_status.model_dump()

    result: UsageControlResult = await internet_usage_service.usage_control(
        internet_usage_control_status["status"],
        profile["phone_number"],
        profile["customer_type"],
    )

    if result.queued:
        return {"message": "queued"}

    return {"message": "done"}
