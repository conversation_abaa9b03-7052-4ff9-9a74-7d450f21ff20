from datetime import datetime
import pytz


def convert_to_consistent_format(date_string):
    date_formats = ["%Y-%m-%dT%H:%M:%S%z", "%Y-%m-%dT%H:%M:%S.%f%z"]

    for date_format in date_formats:
        try:
            return datetime.strptime(date_string, date_format).replace(
                tzinfo=None, hour=0, minute=0, second=0, microsecond=0
            )
        except ValueError:
            pass

    # If none of the formats match, raise an error or handle it accordingly
    raise ValueError("Invalid date format")


def convert_utc_to_iran_time_str(date_string):
    tz = pytz.timezone("Asia/Tehran")
    date_formats = ["%Y-%m-%dT%H:%M:%S%z", "%Y-%m-%dT%H:%M:%S.%f%z"]

    for date_format in date_formats:
        try:

            date = (
                datetime.strptime(date_string, date_format)
                .replace(tzinfo=pytz.UTC)
                .astimezone(tz)
                .replace(tzinfo=None)
            )
            return datetime.strftime(date, date_format)
        except ValueError:
            pass
