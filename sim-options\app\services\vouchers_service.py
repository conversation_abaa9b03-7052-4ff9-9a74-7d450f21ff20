from datetime import datetime, timedelta
import asyncio


class VoucherService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def voucher_history(
        self,
        cow_date,
        customer_type,
        phone_number,
    ):
        today = datetime.now()

        start_date = today - timedelta(days=365)
        if cow_date:
            cow_datetime = datetime.strptime(cow_date, "%Y-%m-%d")
            if cow_datetime > start_date:
                start_date = (
                    cow_datetime + timedelta(days=1)
                    if cow_datetime.day <= today.day
                    else cow_datetime
                )

        if customer_type == "prepaid":
            start_date = start_date.strftime("%d-%b-%Y")
            end_date = today.strftime("%d-%b-%Y")
            tasks = [
                self.eia_client.get_edw_query_details(
                    phone_number, start_date, end_date, "success"
                ),
                self.eia_client.get_edw_query_details(
                    phone_number, start_date, end_date, "pending"
                ),
                self.eia_client.get_edw_query_details(
                    phone_number, start_date, end_date, "failed"
                ),
            ]
            data = await asyncio.gather(*tasks)
            res = data[0] + data[1] + data[2]

        else:
            start_date = start_date.strftime("%Y-%m-%d")
            end_date = today.strftime("%Y-%m-%d")
            res = await self.eia_client.get_voucher_transaction_dtls(
                phone_number, start_date, end_date
            )

        return res
