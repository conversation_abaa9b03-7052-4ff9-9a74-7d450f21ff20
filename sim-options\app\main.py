from fastapi import FastAPI
import sentry_sdk
from sentry_sdk.integrations.asgi import SentryAsgiMiddleware
from sentry_sdk.integrations.httpx import HttpxIntegration
from starlette.middleware.errors import ServerErrorMiddleware
from httpx import ConnectError, ConnectTimeout, ReadError, ReadTimeout
from ngmi_logging.middleware import LoggingMiddleware
from ngmi_http import InvalidUpstreamResponse
from db.mongo.database import close_db_connection, connect_to_db
from core.config import redis, logger
from core.settings import settings
from swagger import servers
from api.api_v1.api import api_router
from api.api_v2.api_v2 import api_v2_router
from health import live, ready
from handlers.exception_handler import (
    global_execution_handler,
    invalid_upstream_response_exception_handler,
    httpx_read_error_exception_handler,
    httpx_read_timeout_exception_handler,
    httpx_connect_timeout_exception_handler,
    httpx_connect_error_exception_handler,
)
from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.setup()
    await redis.connect()
    await connect_to_db()
    await logger.info("startup")
    yield
    await redis.disconnect()
    await close_db_connection()
    await logger.info("shutdown")


app = FastAPI(
    title=settings.SERVICE_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    servers=servers.SERVERS,
    lifespan=lifespan,
)


app.add_middleware(
    ServerErrorMiddleware,
    handler=global_execution_handler,
)
app.add_middleware(
    LoggingMiddleware,
    logger=logger,
)

app.include_router(api_router, prefix=settings.API_V1_STR)
app.include_router(api_v2_router, prefix=settings.API_V2_STR)
app.add_api_route("/v1/live", live)
app.add_api_route("/v1/ready", ready)

###################### Exception Handlers ##############################
app.add_exception_handler(
    InvalidUpstreamResponse, invalid_upstream_response_exception_handler
)
app.add_exception_handler(ReadError, httpx_read_error_exception_handler)
app.add_exception_handler(ReadTimeout, httpx_read_timeout_exception_handler)
app.add_exception_handler(ConnectTimeout, httpx_connect_timeout_exception_handler)
app.add_exception_handler(ConnectError, httpx_connect_error_exception_handler)

########################################################################


sentry_sdk.utils.MAX_STRING_LENGTH = 9999999999999999999999999999999999
sentry_sdk.init(
    dsn=settings.SENTRY_DSN,
    environment=settings.ENVIRONMENT,
    integrations=[HttpxIntegration()],
    traces_sample_rate=float(settings.SENTRY_TRACES_SAMPLE_RATE),
)
app = SentryAsgiMiddleware(app)
