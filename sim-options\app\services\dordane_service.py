from datetime import datetime
from db.mongo.repository import DordaneRepository
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel
from typing import Optional


class DordaneList(BaseModel):
    dordane_numbers: list


class DordaneDetails(BaseModel):
    mtni: bool = True
    sim_type: Optional[str] = None
    customer_type: Optional[str] = None
    notification_phone_number: Optional[str] = None


class AddDordane(BaseModel):
    dordane_eligibility: Optional[bool] = True
    dordane_existence: Optional[bool] = False
    eia_queued: Optional[str] = None
    eia_error_status: Optional[bool] = None
    success: Optional[bool] = False


class RemoveDordane(BaseModel):
    dordane_existence: Optional[bool] = True
    eia_queued: Optional[bool] = False
    eia_error_status: Optional[bool] = None
    success: Optional[str] = False


class DordaneService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def get_customer_info(self, dordaneh_msisdn: str, fields: list = None):

        eia_response = await self.eia_client.get_customer_profile(
            dordaneh_msisdn, fields
        )
        return eia_response

    async def active_dordane_list(
        self,
        session: AsyncIOMotorClient,
        phone_number: str,
        registration_date: datetime,
    ):
        user_info = await DordaneRepository(session=session).get_msisdn_dordane_list(
            phone_number, registration_date
        )

        dordaneh_phone_numbers = []
        if user_info:
            dordaneh_phone_numbers = user_info["dordaneh"]

        return DordaneList(dordane_numbers=dordaneh_phone_numbers)

    async def check_dordane_details(self, dordaneh_msisdn: str, fields: list = None):

        eia_response = await self.get_customer_info(
            dordaneh_msisdn, fields
        )
        if not eia_response["mtni"]:
            return DordaneDetails(mtni=False)
        if eia_response["sim_type"] == "td":
            return DordaneDetails(sim_type="td")
        return DordaneDetails(
            sim_type=eia_response.get("sim_type", ""),
            customer_type=eia_response.get("customer_type", ""),
            notification_phone_number=eia_response.get("notification_phone_number", ""),
        )

    async def add_dordane(
        self,
        session: AsyncIOMotorClient,
        owner_msisdn: str,
        dordane_msisdn: str,
        registration_date: datetime,
        dordaneh_customer_type: str,
    ):
        if owner_msisdn == dordane_msisdn:
            return AddDordane(dordane_eligibility=False)

        item = await DordaneRepository(session=session).exist_in_dordaneh_db(
            owner_msisdn, dordane_msisdn
        )
        if item:
            return AddDordane(dordane_existence=True)

        tariff_code = "1017" if dordaneh_customer_type == "prepaid" else "DBU5045"

        eia_response = await self.eia_client.change_tariff_plan(
            dordane_msisdn, dordaneh_customer_type, tariff_code
        )
        if eia_response["queued"]:
            return AddDordane(eia_queued=eia_response["queued"])
        if eia_response["error_status"]:
            return AddDordane(eia_error_status=eia_response.get("error_status", True))

        dordane = await DordaneRepository(session=session).get_dordane(
            owner_msisdn, registration_date, dordane_msisdn
        )
        if dordane:
            await DordaneRepository(session=session).update_dordane(
                owner_msisdn, registration_date, dordane_msisdn
            )
        else:
            await DordaneRepository(session=session).create_new_dordane(
                owner_msisdn, registration_date, dordane_msisdn
            )

        return AddDordane(success=True)

    async def remove_dordane(
        self,
        session: AsyncIOMotorClient,
        owner_msisdn: str,
        dordane_msisdn: str,
        registration_date: datetime,
    ):
        dordane = await DordaneRepository(session=session).get_dordane(
            owner_msisdn, registration_date, dordane_msisdn
        )
        if not dordane:
            return RemoveDordane(dordane_existence=False)

        dordane_info: DordaneDetails = await self.check_dordane_details(dordane_msisdn)

        tariff_code = (
            "1017" if dordane_info.customer_type == "prepaid" else "DBU5045"
        )

        eia_response = await self.eia_client.change_tariff_plan(
            dordane_msisdn, dordane_info.customer_type, tariff_code
        )
        if eia_response["queued"]:
            return RemoveDordane(eia_queued=eia_response["queued"])
        if eia_response["error_status"]:
            return RemoveDordane(eia_error_status=eia_response.get("error_status", True))

        return RemoveDordane(success=True)
