from pydantic import BaseModel
from pymongo import MongoClient
from ngmi_logging.utils import process_time
from bson.objectid import ObjectId


class MongoCollection:
    def __init__(
        self,
        collection: str,
        session: MongoClient,
        schema: BaseModel = None,
    ) -> None:
        self.collection = collection
        self.session = session
        self.schema = schema

    @staticmethod
    def format(document):
        document["id"] = str(document["_id"])
        del document["_id"]
        return document

    @staticmethod
    def enrich_document_with_id(document, inserted_id):
        document["id"] = str(inserted_id)
        # document["created_at"] = (document.generation_time,)
        return document

    async def create_index(self):
        pass  # override for indexing

    @process_time()
    async def find_all(self):
        documents = self.session.get_collection(self.collection).find({})
        result = []
        async for document in documents:
            if self.schema:
                self.schema(**document)
            result.append(self.format(document))
        return result

    @process_time()
    async def find(self, query):
        documents = self.session.get_collection(self.collection).find(query)
        result = []
        async for document in documents:
            if self.schema:
                self.schema(**document)
            result.append(self.format(document))
        return result

    @process_time()
    async def count(self, query):
        total_count = await self.session.get_collection(
            self.collection
        ).count_documents(query)
        if isinstance(total_count, int):
            return total_count
        else:
            return 0

    @process_time()
    async def find_with_pagination(self, query, skip, limit):
        documents = (
            self.session.get_collection(self.collection)
            .find(query)
            .sort("_id", -1)
            .skip(skip)
            .limit(limit)
        )
        result = []
        async for document in documents:
            if self.schema:
                self.schema(**document)
            result.append(self.format(document))
        return result

    @process_time()
    async def find_one(self, query):
        document = await self.session.get_collection(self.collection).find_one(query)
        if document:
            return self.format(document)
        else:
            return None

    @process_time()
    async def find_by_id(self, id: str):
        document = await self.session.get_collection(self.collection).find_one(
            {"_id": ObjectId(id)}
        )
        if document:
            return self.format(document)
        else:
            return None

    @process_time()
    async def find_one_and_sort(self, query, sort):
        document = await self.session.get_collection(self.collection).find_one(
            query, sort=sort
        )
        if document:
            return self.format(document)
        else:
            return None

    @process_time()
    async def insert_one(self, document):
        if self.schema:
            self.schema(**document)
        result = await self.session.get_collection(self.collection).insert_one(document)
        return self.enrich_document_with_id(document, result.inserted_id)

    @process_time()
    async def update_one(self, query, data, **kwargs):

        result = await self.session.get_collection(self.collection).update_one(
            query, {"$set": data}, **kwargs
        )
        return result
    
    @process_time()
    async def update_one_by_push(self, query, data, **kwargs):

        result = await self.session.get_collection(self.collection).update_one(
            query, {"$push": data}, **kwargs
        )
        return result
    
    @process_time()
    async def update_one_by_pull(self, query, data, **kwargs):

        result = await self.session.get_collection(self.collection).update_one(
            query, {"$pull": data}, **kwargs
        )
        return result

    @process_time()
    async def update_by_id(self, id: str, data, **kwargs):
        result = await self.session.get_collection(self.collection).update_one(
            {"_id": ObjectId(id)}, {"$set": data}, **kwargs
        )
        return result

    @process_time()
    async def replace_one(self, query, data):
        result = await self.session.get_collection(self.collection).replace_one(
            query, data, upsert=True
        )
        return result

    @process_time()
    async def update_many(self, query, data):

        result = await self.session.get_collection(self.collection).update_many(
            query,
            {"$set": data},
        )
        return result
    
    @process_time()
    async def update_many_(self, query, data):

        result = await self.session.get_collection(self.collection).update_many(
            query,
            data,
        )
        return result

    @process_time()
    async def delete_one(self, query):

        result = await self.session.get_collection(self.collection).delete_one(
            query,
        )
        return result

    @process_time()
    async def delete_many(self, query):

        result = await self.session.get_collection(self.collection).delete_many(
            query,
        )
        return result
