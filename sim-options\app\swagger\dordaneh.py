get_dordaneh_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
}

post_dordaneh_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Dordaneh activation failed",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/dordaneh/activation/failed",
                        "title": "Dordaneh activation failed",
                        "status": "400",
                        "message": "You can't add this number as a Dordaneh. Please seek support from the online chat",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                        "title": "OTP is invalid",
                    },
                ]
            }
        },
    },
    200: {
        "description": "added a dordaneh successfully",
        "content": {"application/json": {"example": {}}},
    },
}
delete_dordaneh_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Dordaneh deactivation failed",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/dordaneh/deactivation/failed",
                    "title": "Dordaneh deactivation failed",
                    "status": "400",
                    "message": "You can't remove this number from your Dordanehs. Please seek support from the online chat",
                }
            }
        },
    },
    200: {
        "description": "removed a dordaneh successfully",
        "content": {"application/json": {"example": {}}},
    },
}
