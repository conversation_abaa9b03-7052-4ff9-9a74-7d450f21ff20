from datetime import datetime, timedelta
import asyncio
from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorClient

from utils import (
    persian_text_normalizer,
    termination_check_number,
    termination_check_entry,
    send_sms_otp,
    process_subscribers,
    list_of_eligible_subscribers,
    is_invalid_otp,
    handle_prepaid_termination,
    handle_postpaid_termination,
    handle_postpaid_cancellation,
    is_valid_msisdn,
    is_valid_phone_number,
    create_error_response,
    handle_termination_failure,
    handle_prepaid_cancellation,
)
from db.mongo.repository import TerminationRepository
from core.settings import settings

from third_parties.aat.aat_client import AATClient

registered_numbers_type_map = {"LT": "Voice", "NORML": "Voice", "TDD": "TDD"}
termination_grace_period = 1
# reported_grace_period = 10
reported_grace_period = 3


class TerminationOTPResult(BaseModel):
    error_status: bool = False
    termination_possibility: bool = False
    reason: str = ""
    data: dict = {}


class TerminationCheckEntry(BaseModel):
    error_status: bool = False
    termination_possibility: bool = False
    reason: str = ""


class TerminationSubmit(BaseModel):
    error_status: bool = False
    error_message: str = ""
    own_number: bool = True
    information_lack: bool = False
    operation_failed: bool = False
    reason: str = ""
    self_reason: str = ""
    subscriber_details: list = []


class TerminationCancell(BaseModel):
    error_status: bool = False
    own_number: bool = False
    operation_failed: bool = False
    db_failure: bool = False
    data: list = []


class RegisteredNumbersV2NewService:
    def __init__(self, eia_client, hsdp_client, aat_client):
        self.eia_client = eia_client
        self.hsdp_client = hsdp_client
        self.aat_client: AATClient = aat_client

    async def get_customer_info(self, phone_number, fields=None):
        eia_response = await self.eia_client.get_customer_profile(
            phone_number=phone_number
        )
        return eia_response

    async def termination_check_number(
        self,
        session: AsyncIOMotorClient,
        phone_number,
        subscriber_phone_number,
        language,
    ):
        customer_profile = await self.get_customer_info(
            phone_number=subscriber_phone_number
        )
        sub_nid = customer_profile["national_id"]

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        if sub_nid != nid:
            return TerminationOTPResult(error_status=True, reason="failed_due_not_own")

        query = {
            "msisdn": phone_number,
            "status": "in-progress",
        }
        termination_requests = await TerminationRepository(
            session
        ).get_termination_requests(query)
        if termination_requests:
            return TerminationOTPResult(reason="failed_due_duplication")
        now = datetime.now()
        last_day = now - timedelta(days=1)
        query = {
            "national_id": nid,
            "request_datetime": {"$gte": last_day, "$lte": now},
            "status": {"$in": ["cancelled", "in-progress"]},
        }
        today_requests = await TerminationRepository(
            session
        ).count_termination_requests(query)
        if today_requests > int(settings.TERMINATION_DAILY_LIMIT):
            return TerminationOTPResult(reason="failed_due_requests")

        last_week = now - timedelta(days=7)
        query = {
            "national_id": nid,
            "request_datetime": {"$gte": last_week, "$lte": now},
            "status": {"$in": ["cancelled", "in-progress"]},
        }
        this_week_requests = await TerminationRepository(
            session
        ).count_termination_requests(query)
        if this_week_requests > int(settings.TERMINATION_WEEKLY_LIMIT):
            return TerminationOTPResult(reason="failed_due_requests")

        if customer_profile["profile_type"] != "individual":
            return TerminationOTPResult(reason="failed_due_profile_type")

        if customer_profile["customer_type"] == "td":
            return TerminationOTPResult(reason="failed_due_sim_type")

        status = await self.eia_client.suspension_status(phone_number=phone_number)
        if status["error_status"]:
            return TerminationOTPResult(error_status=True)

        elif "data" in status:
            if not status["data"]["is_active"]:
                reasons = []
                reasons_res = await self.eia_client.suspension_reasons(
                    phone_number=phone_number
                )
                if not reasons_res["error_status"]:
                    reasons = reasons_res["data"]
                for reason in reasons:
                    reason["description"] = reason["description"][language]
                    reason["guidance"] = reason["guidance"][language]
                return TerminationOTPResult(
                    reason="failed_due_activation",
                    data={
                        "is_active": status["data"]["is_active"],
                        "reasons": reasons,
                    },
                )

        else:
            return TerminationOTPResult(error_status=True)

        shared_account_status = await self.eia_client.shared_account_get_status(
            phone_number=phone_number
        )
        if shared_account_status["error_status"]:
            return TerminationOTPResult(error_status=True)

        shared_account_status = shared_account_status["data"]["is_activated"]
        if shared_account_status:
            return TerminationOTPResult(reason="faild_due_sharedaccount")

        if customer_profile["customer_type"] == "postpaid":
            account_details = await self.eia_client.get_account_details(
                phone_number=phone_number,
                customer_type="postpaid",
                language=language,
            )
            if (account_details["current_balance"] > 0) or (
                account_details["outstanding_balance"] > 0
            ):
                amount = (
                    account_details["current_balance"]
                    if account_details["current_balance"]
                    else account_details["outstanding_balance"]
                )
                # bill = "صورت حساب" if language == "fa" else "bill"
                return TerminationOTPResult(
                    reason="faild_due_debt",
                    data={"debt_type": "bill", "amount": amount},
                )

        else:
            current_debt = 0
            hsdp_jresp, hsdp_resp = await self.hsdp_client.hsdp_get_fake_id(
                phone_number=phone_number,
            )
            fake_id_res = {"error_status": False}
            if "success" in hsdp_resp.lower() and not "failed" in hsdp_resp.lower():
                try:
                    fake_id = hsdp_jresp["soapenv:Envelope"]["soapenv:Body"][
                        "ns1:queryUserIdListReturn"
                    ]["ns5:userIDs"]["ns1:item"]["ns6:ID"]
                    fake_id_res["data"] = fake_id["#text"]
                except:
                    fake_id_res["error_status"] = True
            else:
                fake_id_res["error_status"] = True

            if not fake_id_res["error_status"] and fake_id_res["data"].startswith("f-"):
                fake_id = fake_id_res["data"]
                aat_res = await self.aat_client.aat_qualification_check(
                    fake_id=fake_id,
                    phone_number=phone_number,
                )

                aat_final_res = {"error_status": False}

                if aat_res["data"]["status_message"] == "success":
                    try:
                        aat_final_res["data"] = {
                            "amount_qualified": aat_res["data"]["amount_qualified"],
                            "loan_balance": aat_res["data"]["loan_balance"],
                            "code": aat_res["data"]["code"],
                        }
                    except:
                        aat_final_res["error_status"] = True
                else:
                    try:
                        aat_final_res["data"] = {
                            "loan_balance": aat_res["data"]["loan_balance"],
                        }
                    except:
                        aat_final_res["error_status"] = True

                if aat_final_res["error_status"]:
                    return TerminationOTPResult(error_status=True)

                if (
                    not aat_final_res["error_status"]
                    and "loan_balance" in aat_res["data"]
                ):
                    current_debt = aat_final_res["data"]["loan_balance"]

            if current_debt > 0:
                # advance_air_time = (
                #     "خدمات اضطراری" if language == "fa" else "advance air time"
                # )
                return TerminationOTPResult(
                    reason="faild_due_debt",
                    data={"debt_type": "advance_air_time", "amount": current_debt},
                )
        return TerminationOTPResult(termination_possibility=True)

    async def termination_check_entry(
        self, phone_number, national_id, first_name, last_name, father_name
    ):

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        if customer_profile["national_id"].lower() != national_id.lower():
            return TerminationCheckEntry(
                termination_possibility=False, reason="failed_due_nid_mismatch"
            )

        first_name = persian_text_normalizer(first_name)
        last_name = persian_text_normalizer(last_name)
        father_name = persian_text_normalizer(father_name)
        enterd_first_name = persian_text_normalizer(customer_profile["first_name"])
        enterd_last_name = persian_text_normalizer(customer_profile["last_name"])
        enterd_father_name = persian_text_normalizer(customer_profile["father_name"])

        if (
            first_name != enterd_first_name
            or last_name != enterd_last_name
            or father_name != enterd_father_name
        ):
            return TerminationCheckEntry(
                termination_possibility=False, reason="failed_due_identity_mismatch"
            )

        return TerminationCheckEntry(termination_possibility=True)

    async def inquiry(self, session: AsyncIOMotorClient, phone_number):

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        subscriber_details = await self.eia_client.list_of_subsciber_details(
            nid, phone_number
        )

        phone_numbers = []
        result = []
        type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}

        for pn in subscriber_details:
            if pn["phone_number"] not in phone_numbers and pn["status"] in [
                "Active",
                "Soft-suspended",
                "Suspended",
                "waiting-for-first-callback",
            ]:
                pn["type"] = type_map.get(pn["type"])
                phone_numbers.append(pn["phone_number"])
                msisdn = pn["phone_number"]

                if pn["type"] == "TDD":
                    pn["termination_status"] = "not_eligible"
                else:
                    termination_request = await TerminationRepository(
                        session=session
                    ).find_one_request(msisdn)
                    if termination_request:
                        now = datetime.now().replace(microsecond=0, second=0)
                        termination_request_date = termination_request["request_datetime"]
                        if termination_request["status"] == "reported":
                            if (
                                now - termination_request_date
                            # ).days > reported_grace_period:
                            ).total_seconds() / 60 > reported_grace_period:
                                pn["termination_status"] = "not_requested"
                            else:
                                pn["termination_status"] = "pending"
                        else:
                            if (now - termination_request_date).days < termination_grace_period:
                                pn["termination_status"] =  "grace_period"
                            else:
                                pn["termination_status"] =  "pending"
                    else:
                        pn["termination_status"] =  "not_requested" 

                result.append(pn)

        return result

    async def termination_submit_req(
        self,
        session: AsyncIOMotorClient,
        phone_number,
        phone_customer_type,
        phone_registration_date,
        phone_profile_type,
        national_id,
        subscriber_phone_number,
        first_name,
        last_name,
        father_name,
        language,
        reason,
    ):
        # customer_profile = await self.get_customer_info(phone_number=phone_number)
        customer_type = phone_customer_type
        registration_date = phone_registration_date
        profile_type = phone_profile_type

        if customer_type == "prepaid":
            subscribers_registered_numbers = (
                await self.eia_client.list_of_subsciber_details(
                    national_id, subscriber_phone_number
                )
            )
            own_number = any(
                phone_number == registered_number["phone_number"]
                for registered_number in subscribers_registered_numbers
            )

            if not own_number:
                return TerminationSubmit(error_status=True, own_number=False)

            termination_check_status = await self.termination_check_entry(
                phone_number, national_id, first_name, last_name, father_name
            )

            if termination_check_status.error_status:
                return TerminationSubmit(error_status=True, information_lack=True)

            if not termination_check_status.termination_possibility:
                return TerminationSubmit(
                    error_status=True, reason=termination_check_status.reason
                )

            res = await self.eia_client.online_termination_request(
                subscriber_code=subscriber_phone_number, phone_number=phone_number
            )
            if res["error_status"]:
                return TerminationSubmit(error_status=True, operation_failed=True)

            return TerminationSubmit()
        else:
            termination_check_status = await self.termination_check_entry(
                phone_number, national_id, first_name, last_name, father_name
            )

            if termination_check_status.error_status:
                return TerminationSubmit(error_status=True, information_lack=True)

            if not termination_check_status.termination_possibility:

                if termination_check_status.reason in [
                    "failed_due_sim_type",
                    "failed_due_duplication",
                    "failed_due_not_own",
                ]:
                    return TerminationSubmit(
                        error_status=True, self_reason=termination_check_status.reason
                    )
                return TerminationSubmit(
                    error_status=True, reason=termination_check_status.reason
                )

            termination_check_status = await self.termination_check_number(
                session=session,
                phone_number=phone_number,
                subscriber_phone_number=subscriber_phone_number,
                language=language,
            )

            if termination_check_status.error_status:
                return TerminationSubmit(error_status=True, information_lack=True)

            if not termination_check_status.termination_possibility:
                return TerminationSubmit(
                    error_status=True, reason=termination_check_status.reason
                )

            now = datetime.now().replace(microsecond=0)

            query = {
                "msisdn": phone_number,
                "registration_date": registration_date,
                "profile_type": profile_type,
                "customer_type": customer_type,
                "request_datetime": now,
                "national_id": national_id,
                "requested_by": subscriber_phone_number,
                "cancelled_by": "",
                "first_name": first_name,
                "last_name": last_name,
                "father_name": father_name,
                "reason": reason,
                "status": "in-progress",
            }

            await TerminationRepository(session=session).add_termination_request(query)
            try:
                res = await self.eia_client.list_of_subsciber_details(
                    national_id, phone_number
                )
                return TerminationSubmit(subscriber_details=res)
            except Exception as e:
                await TerminationSubmit(
                    error_status=True, error_message=f"termination_issue: {str(e)}"
                )

    async def termination_cancell(
        self, session: AsyncIOMotorClient, phone_number, profile_phone_number
    ):
        customer_profile = await self.get_customer_info(phone_number=phone_number)
        customer_type = customer_profile["customer_type"]
        nid = customer_profile["national_id"]

        if customer_type == "prepaid":
            subscribers_registered_numbers = (
                await self.eia_client.list_of_subsciber_details(
                    nid, profile_phone_number
                )
            )

            if not any(
                phone_number == registered_number["phone_number"]
                for registered_number in subscribers_registered_numbers
            ):
                return TerminationCancell(error_status=True)

            res = await self.eia_client.cancel_online_termination(
                subscriber=profile_phone_number, phone_number=phone_number
            )
            if res["error_status"]:
                return TerminationCancell(error_status=True, operation_failed=True)
            return TerminationCancell()

        else:
            cancelled_from_db = await TerminationRepository(
                session
            ).cancell_termination_request(phone_number, profile_phone_number)
            if not cancelled_from_db:
                return TerminationCancell(error_status=True, db_failure=True)

            res = await self.eia_client.list_of_subsciber_details(
                customer_profile["national_id"], phone_number
            )

            final_recipients = []
            own_number = False
            for pn in res:
                if profile_phone_number == pn["phone_number"]:
                    own_number = True
                if pn["type"] != "TDD":
                    final_recipients.append(pn["phone_number"])

            if not own_number:
                return TerminationCancell(error_status=True, own_number=False)

            return TerminationCancell(data=final_recipients)

    async def delink(self, session: AsyncIOMotorClient, phone_number):
        try:
            await TerminationRepository(session=session).delink_update(phone_number)

            return True
        except:
            return False
   
    async def registered_status(self, session: AsyncIOMotorClient, phone_number):
        customer_profile = await self.get_customer_info(
            phone_number=phone_number
        )

        nid = customer_profile["national_id"]

        subscriber_details = await self.eia_client.list_of_subsciber_details(
            nid, phone_number
        )

        phone_numbers = []
        pre_paid_phone_numbers = []
        result = []
        type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}

        for pn in subscriber_details:
            if (pn["phone_number"] not in phone_numbers and
                    pn["status"] in ["Active", "Soft-suspended", "Suspended", "waiting-for-first-callback"]  and
                    pn["type"] != "TDD" ) :
                pn["type"] = type_map.get(pn["type"])
                phone_numbers.append(pn["phone_number"])
                msisdn = pn["phone_number"]
                customer_profile = await self.get_customer_info(phone_number=msisdn)
                customer_type = customer_profile["customer_type"]
                if customer_type == "prepaid":
                    pre_paid_phone_numbers.append(pn["phone_number"])
                else:
                    # termination_status = await TerminationRepository(session= session).get_termination_status(msisdn= msisdn)
                    # pn["termination_status"] =  termination_status
                    termination_request = await TerminationRepository(session= session).find_one_request(msisdn= msisdn)
                    if termination_request:
                        now = datetime.now().replace(microsecond=0, second=0)
                        termination_request_date = termination_request["request_datetime"]
                        if termination_request["status"] == "reported":
                            if (
                                now - termination_request_date
                            # ).days > reported_grace_period:
                            ).total_seconds() / 60 > reported_grace_period:
                                pn["termination_status"] = "not_requested"
                            else:
                                pn["termination_status"] = "pending"
                        else:
                            if (now - termination_request_date).days < termination_grace_period:
                                pn["termination_status"] =  "grace_period"
                            else:
                                pn["termination_status"] =  "pending"
                    else:
                        pn["termination_status"] =  "not_requested" 
                    result.append(pn)


        tasks = []
        for pre_paid_phone_number in pre_paid_phone_numbers:
            tasks.append(self.eia_client.pending_online_termination_request(pre_paid_phone_number))
                    
        tasks_result = await asyncio.gather(*tasks, return_exceptions=True)  
        for task_result in tasks_result:    
            if not isinstance(task_result, Exception):
                if not task_result["error_status"]:
                    data = task_result["data"]
                    result.append(
                        {
                            "phone_number": data["phone_number"],
                            "termination_status": data["termination_status"],
                        }
                    )   

        return result
    
    # async def registered_status(self, session: AsyncIOMotorClient, phone_number):
    #     customer_profile = await self.get_customer_info(phone_number=phone_number)

    #     nid = customer_profile["national_id"]

    #     subscriber_details = await self.eia_client.list_of_subsciber_details(
    #         nid, phone_number
    #     )

    #     phone_numbers = []
    #     pre_paid_phone_numbers = []
    #     result = []
    #     type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}

    #     for pn in subscriber_details:
    #         if (
    #             pn["phone_number"] not in phone_numbers
    #             and pn["status"]
    #             in [
    #                 "Active",
    #                 "Soft-suspended",
    #                 "Suspended",
    #                 "waiting-for-first-callback",
    #             ]
    #             and pn["type"] != "TDD"
    #         ):
    #             pn["type"] = type_map.get(pn["type"])
    #             phone_numbers.append(pn["phone_number"])
    #             msisdn = pn["phone_number"]
    #             customer_profile = await self.get_customer_info(phone_number=msisdn)
    #             customer_type = customer_profile["customer_type"]
    #             if customer_type == "prepaid":
    #                 pre_paid_phone_numbers.append(pn["phone_number"])
    #             else:
    #                 termination_status = await TerminationRepository(
    #                     session=session
    #                 ).get_termination_status(msisdn=msisdn)
    #                 pn["termination_status"] = termination_status
    #             result.append(pn)

    #     tasks = []
    #     for pre_paid_phone_number in pre_paid_phone_numbers:
    #         tasks.append(
    #             self.eia_client.pending_online_termination_request(
    #                 pre_paid_phone_number
    #             )
    #         )

    #     tasks_result = await asyncio.gather(*tasks, return_exceptions=True)
    #     for task_result in tasks_result:
    #         if not isinstance(task_result, Exception):
    #             if not task_result["error_status"]:
    #                 data = task_result["data"]
    #                 result.append(
    #                     {
    #                         "phone_number": data["phone_number"],
    #                         "termination_status": data["termination_status"],
    #                     }
    #                 )

    #     return result
