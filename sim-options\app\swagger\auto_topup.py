auto_topup_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "auto topup status",
        "content": {
            "application/json": {
                "example": [
                    {
                        "status": "activated",
                        "recharge_type": "normal",
                        "amount": "1000",
                        "threshold_id": "2",
                        "bank_name": "Pardakht Saman",
                        "bank_id": "69",
                        "service_id": "1",
                    },
                    {
                        "status": "activated",
                        "recharge_type": "normal",
                        "amount": "100000",
                        "threshold_id": "5",
                        "bank_name": "FaraBoom",
                        "bank_id": "63",
                        "service_id": "1",
                    },
                ]
            }
        },
    },
}
deactivate_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "top-up failed",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/auto_topup/deregister/failed",
                    "title": "Auto top-up deregistration failed",
                }
            }
        },
    },
    200: {
        "description": "auto topup status",
        "content": {"application/json": {"example": {"message": "done"}}},
    },
}
