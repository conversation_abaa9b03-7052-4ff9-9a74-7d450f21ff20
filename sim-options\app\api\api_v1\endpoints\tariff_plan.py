import asyncio
from datetime import datetime
from typing import Any, Annotated
from core.config import authorization, eia_client
from fastapi import APIRouter
from fastapi import Depends
from fastapi.responses import JSONResponse
from schemas import TariffPlan
from swagger import tariff_plan

from services import TtarifPlanService
from core.config import get_tarif_plan_service

router = APIRouter()


@router.get("/tariff_plan", responses=tariff_plan.get_tariff_plan_sample_response)
async def get_tariff_plan(
    tarif_plan_service: Annotated[TtarifPlanService, Depends(get_tarif_plan_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber current and eligible tariff plans

    Raises:

        401: If JWT token is invalid

    Returns:

        current_tariff_plan: Represents subscriber current tariff plan
        eligible_tariff_plans: Represents subscriber eligible plans list
        eligible_tariff_plan.id: Represents plan id
        eligible_tariff_plan.name: Represents plan name
        eligible_tariff_plan.description: Represents plan description

    """
    res = await tarif_plan_service.get_tarif(
        phone_number=profile["phone_number"],
        customer_type=profile["customer_type"],
        language=profile["language"],
        fields=["current_tariff_plan"],
    )
    return res


@router.post("/tariff_plan", responses=tariff_plan.post_tariff_plan_sample_responses)
async def set_tariff_plan(
    tariff_plan: TariffPlan,
    tarif_plan_service: Annotated[TtarifPlanService, Depends(get_tarif_plan_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Change subscriber tariff plan

    Raises:

        401: If JWT token is invalid

    Returns:

        message: Represents operation status

    """
    tariff_plan = tariff_plan.model_dump()
    phone_number = profile["phone_number"]
    customer_type = profile["customer_type"]
    language = profile["language"]
    tariff_code = tariff_plan["id"]

    result = await tarif_plan_service.change_plan(
        phone_number=phone_number,
        customer_type=customer_type,
        lang=language,
        tariff_code=tariff_code,
    )

    if result["queued"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account/plan_change/in_process",
                "title": "There is already a change tariff plan request in-process",
                "message": "There is already a change tariff plan request in-process.",
            },
        )
    if result["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account/plan_change/too_many",
                "title": "There was a error in changing tariff plan request.",
                "message": "There was an error in changing tariff plan request, please ask support form online chat",
            },
        )
    return {"message": "done"}


@router.get(
    "/tariff_plan/history", responses=tariff_plan.tariff_plan_history_sample_responses
)
async def tariff_plan_history(
    tarif_plan_service: Annotated[TtarifPlanService, Depends(get_tarif_plan_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber tariff plan history

    Raises:

        401: If JWT token is invalid

    Returns:

        current_tariff_plan: Represents subscriber current tariff plan
        eligible_tariff_plans: Represents subscriber eligible plans list
        eligible_tariff_plan.id: Represents plan id
        eligible_tariff_plan.name: Represents plan name
        eligible_tariff_plan.description: Represents plan description

    """

    res = await tarif_plan_service.get_history(
        phone_number=profile["phone_number"],
        lang=profile["language"],
        cow_date=profile["mtni"]["cow_date"],
    )

    return res
