from typing import Any
from fastapi.responses import JSONResponse
from fastapi import APIRouter
from fastapi import Depends
from core.config import authorization, eia_client
from schemas import RoamingStatus
from swagger import roaming

from services import RoamingService
from core.config import get_roaming_service
from services.roaming_service import RoamingResult

router = APIRouter()


@router.post("/roaming", responses=roaming.roaming_sample_responses)
async def roaming(
    roaming_status: RoamingStatus,
    roaming_service: RoamingService = Depends(get_roaming_service),
    profile: dict = Depends(authorization),
) -> Any:
    """Change subscriber roaming status
    - **status**: Defines subscriber roaming status

    Raises:

        401: If JWT token is invalid

    Returns:

        message: Represents operation status

    """
    roaming_status = roaming_status.model_dump()

    res: RoamingResult = await roaming_service.set_network(
        status=roaming_status["status"],
        customer_type=profile["customer_type"],
        phone_number=profile["phone_number"],
    )

    if res.status_code == -1:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/roaming/change_status/failed",
                "title": "Changing roaming status failed",
                "status": "400",
                "message": "You can't change the status of roaming at the moment",
            },
        )
    elif res.status_code == 11101:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/roaming/change_status/failed",
                "title": "Changing roaming status failed",
                "status": "400",
                "message": "a request is already in queue against Service",
            },
        )
    elif res.status_code == 0:
        return {"message": "done"}
