from fastapi import APIRouter, Depends, status
from fastapi.responses import J<PERSON><PERSON><PERSON>po<PERSON>, Response

from typing import Any, Annotated

from core.config import authorization, eia_client
from swagger import suspension_reason
from services import SuspensionService
from core.config import get_suspension_service
from services.suspension_reason_service import SuspensionR<PERSON>ult
from schemas.suspension_status import SuspensionR<PERSON>ponse
from handlers.custom_handler import CustomHTTPException

router = APIRouter()


@router.get(
    "",
    status_code=status.HTTP_200_OK,
    response_model=SuspensionResponse,
    responses=suspension_reason.suspension_reason_sample_responses,
)
async def suspension_reasons(
    suspension_reason_service: Annotated[
        SuspensionService, Depends(get_suspension_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> SuspensionResponse | CustomHTTPException:

    result = await suspension_reason_service.suspension_status(
        phone_number=profile["phone_number"], language=profile["language"]
    )

    if result.error_status:
        raise CustomHTTPException(status_code=status.HTTP_400_BAD_REQUEST, title="")
    return SuspensionResponse(**result.model_dump())
