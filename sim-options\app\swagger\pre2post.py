upgrade_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/pre2post/validation/failed_due_debt",
                        "title": "Pre2post migration failed due to outstanding debt.",
                        "params": {"current_debt": "2000"},
                    },
                    {
                        "type": "https://my.irancell.ir/errors/pre2post/validation/failed_due_activation",
                        "title": "Pre2post migration failed due to activation status.",
                    },
                ]
            }
        },
    },
    200: {
        "description": "Pre2Post Plans",
        "content": {
            "application/json": {
                "example": {
                    "offer_id": "PRPOCR",
                    "offer_name": "Tariff Plan PRPOCR02",
                    "offer_amount": "1000",
                    "offer_credit_limit": "110000",
                }
            }
        },
    },
}
