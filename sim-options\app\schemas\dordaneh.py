from pydantic import BaseModel, field_validator


class DordanehRequestInput(BaseModel):
    phone_number: str
    otp: str

    @field_validator("phone_number")
    def phone_number_valid(cls, v):
        if len(v) != 12 or not v.startswith("98"):
            raise ValueError("Phone number is invalid")
        return v

    @field_validator("otp")
    def otp_valid(cls, v):
        if len(v) != 4:
            raise ValueError("OTP must be 4 digits long")
        return v


class DordanehInput(BaseModel):
    phone_number: str

    @field_validator("phone_number")
    def phone_number_valid(cls, v):
        if v:
            if not ((len(v) == 12 and v.startswith("98")) or len(v) == 10):
                raise ValueError("Phone number is invalid")
        return v

class DordaneResponse(BaseModel):
    dordane_numbers: list