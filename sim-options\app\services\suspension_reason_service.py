from datetime import datetime
from pydantic import BaseModel


class SuspensionResult(BaseModel):
    error_status: bool = False
    is_active: bool = False
    reasons: list = []


class UpdateSuspensionResult(BaseModel):
    soft_error_status: bool = False
    soft_in_progress: bool = False
    revoke_error_status: bool = False
    revoke_in_progress: bool = False
    suspension_status: bool = False
    suspension_date: bool = False


class SuspensionService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def suspension_status(self, phone_number, language):
        status = await self.eia_client.suspension_status(phone_number)

        if status["error_status"]:
            return SuspensionResult(error_status=True)
        elif "data" in status:
            if status["data"]["is_active"]:
                return SuspensionResult(is_active=status["data"]["is_active"])
            else:
                reasons = []

                reasons_res = await self.eia_client.suspension_reasons(
                    phone_number=phone_number
                )
                if not reasons_res["error_status"]:
                    reasons = reasons_res["data"]

                for reason in reasons:
                    reason["description"] = reason["description"][language]
                    reason["guidance"] = reason["guidance"][language]
                return SuspensionResult(
                    is_active=status["data"]["is_active"], reasons=reasons
                )
        else:
            return SuspensionResult(error_status=True)

    async def get_status(self, phone_number, customer_type):
        eia_response = await self.eia_client.get_line_status(
            phone_number, customer_type
        )
        return eia_response

    async def update_suspension(self, phone_number, reason, customer_type, status=None):
        final_reason = "B_VOLN"  # temporary reason
        if reason == "stolen":
            final_reason = "B_STLN"
        if status:
            soft_line_response = await self.eia_client.soft_line_suspension(
                phone_number, final_reason
            )
            if soft_line_response["error_status"]:
                # UpdateSuspensionResult(soft_error_status=True)
                if soft_line_response["in_progress"]:
                    return UpdateSuspensionResult(soft_error_status=True, soft_in_progress=True)
                else:
                    return UpdateSuspensionResult(soft_error_status=True)
            else:
                return UpdateSuspensionResult()
        else:
            line_status = await self.eia_client.get_line_status(
                phone_number, customer_type
            )

            if not line_status["suspension_status"]:
                return UpdateSuspensionResult(suspension_status=False)
            date = "/".join(line_status["date"].split()[0].split("-")[::-1])
            if not date:
                return UpdateSuspensionResult(suspension_date=False)

            revok_response = await self.eia_client.revoke_soft_suspension(
                phone_number, final_reason, date
            )
            if revok_response["error_status"]:
                # UpdateSuspensionResult(revoke_error_status=True)
                if revok_response["in_progress"]:
                    return UpdateSuspensionResult(revoke_error_status=True, revoke_in_progress=True)
                else:
                    return UpdateSuspensionResult(revoke_error_status=True)
                
            return UpdateSuspensionResult()
