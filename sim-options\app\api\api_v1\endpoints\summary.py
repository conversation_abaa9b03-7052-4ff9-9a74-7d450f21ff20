import asyncio
from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends
from core.config import authorization, eia_client, ecp_client
from swagger import summary

from services import SummaryService
from core.config import get_summary_service
from services.summary import SummaryResult

router = APIRouter()


@router.get(
    "/summary", response_model=SummaryResult, responses=summary.summary_sample_responses
)
async def summary(
    summary_service: Annotated[SummaryService, Depends(get_summary_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> SummaryResult:
    """Retrieve subscriber sim options summary

    Raises:

        401: If JWT token is invalid

    Returns:

        roaming_status: Represents subscriber roaming status
        internet_usage_control: Represents subscriber internet usage control status
        promotional_sms: Represents subscriber promotional sms status

    """

    res = await summary_service.summary_details(
        phone_number=profile["phone_number"],
        customer_type=profile["customer_type"],
        language=profile["language"],
    )

    return res
