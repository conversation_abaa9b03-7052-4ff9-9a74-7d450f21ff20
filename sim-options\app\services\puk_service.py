from datetime import datetime
from core.config import bom_client


class PukService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def account_details(self, phone_number, customer_type, language, fields=None):
        eia_response = await self.eia_client.get_account_details(
            phone_number,
            customer_type,
            language,
            fields,
        )
        return eia_response
