from typing import Any, Annotated
from datetime import datetime
from fastapi import APIRouter
from fastapi import HTT<PERSON>Exception
from fastapi import Depends
from fastapi.responses import JSONResponse, Response
from core.config import authorization, aat_client, eia_client, hsdp_client
from core.settings import settings
from swagger import pre2post

from services import Pre2PostService
from core.config import get_pre2post_service
from services.pre2post_service import UpgradeResult

router = APIRouter()


@router.get("/upgrade", responses=pre2post.upgrade_sample_response)
async def get_pre2post(
    pre2post_service: Annotated[Pre2PostService, Depends(get_pre2post_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    res: UpgradeResult = await pre2post_service.upgrade_pre2post(
        phone_number=profile["phone_number"],
        fields=["operation_status"],
        language=profile["language"],
    )

    if res.error_status:
        return HTTPException(status_code=500, detail="Internal Server Error")
    if res.outstanding_debt:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/pre2post/validation/failed_due_debt",
                "title": "Pre2post migration failed due to outstanding debt.",
                "params": {"current_debt": res.current_debt},
            },
        )
    if not res.activation_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/pre2post/validation/failed_due_activation",
                "title": "Pre2post migration failed due to activation status.",
            },
        )

    return res
