from pydantic import BaseModel
from fastapi.requests import Request
from fastapi.responses import JSONResponse


class ErrorResponse(BaseModel):
    type: str | None = None
    title: str | None = None
    detail: str = ""
    params: dict | None = None


class CustomHTTPException(Exception):
    def __init__(
        self,
        status_code: int,
        type: str | None = None,
        title: str | None = None,
        details: str = "",
        params: dict | None = None,
    ):
        self.status_code = status_code
        self.type = type
        self.title = title
        self.details = details
        self.params = params


async def custom_http_exception_handler(request: Request, exc: CustomHTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=CustomHTTPException(
            type=exc.type, title=exc.title, details=exc.details, params=exc.params
        ).model_dump(exclude_none=True),
    )
