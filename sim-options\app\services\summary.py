from datetime import datetime
import asyncio
from pydantic import BaseModel


class SummaryResult(BaseModel):
    promotional_sms: bool = False
    internet_usage_control: bool = False
    roaming_status: bool = False

class SummaryService:
    def __init__(self, ecp_client, eia_client):
        self.ecp_client = ecp_client
        self.eia_client = eia_client

    async def summary_details(self, phone_number, customer_type, language):
        res = {}
        tasks = [
            self.eia_client.get_network_services(
                phone_number=phone_number, customer_type=customer_type
            ),
            self.eia_client.get_account_details(
                phone_number=phone_number,
                customer_type=customer_type,
                language=language,
                fields=["internet_usage_control"],
            ),
            self.ecp_client.get_catsubcat_status(phone_number),
        ]
        data = await asyncio.gather(*tasks, return_exceptions=True)
        for item in data:
            if not isinstance(item, Exception):
                res.update(item)
            else:
                pass

        return SummaryResult(**res)
