import json
import xmltodict

from ngmi_http import HTTPBase
from .parser import XML

class HSDPClient(HTTPBase):
    def __init__(self,base_url, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.xml_proccesor = XML()

    async def _call_api(
        self,
        url,
        payload,
        headers,
        api_name,
        subject,
        request_parser=None
    ):
        if not request_parser:
            request_parser = self.xml_proccesor.hsdp_request_parser
        response = await self._call_soap_api(
            url=url,
            payload=payload,
            headers=headers,
            api_name=api_name,
            subject=subject,
            service="HSDP",
            request_parser=request_parser
        )

        return (
            json.loads(json.dumps(xmltodict.parse(response["data"]))),
            response["data"],
        )

    async def hsdp_get_fake_id(self, phone_number):
        headers = {
            "Content-Type": "text/xml",
        }
        url = self.base_url + "/EndUserServices"
        body = f'<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"\n    xmlns:soap="http://soapheader.ib.sdp.huawei.com"\n    xmlns:end="http://enduserservices.ib.sdp.huawei.com"\n    xmlns:req="http://request.enduserservices.ib.sdp.huawei.com"\n    xmlns:sch="http://schema.ib.sdp.huawei.com">\n   <soapenv:Header>\n      <soap:IBSoapHeader>\n<sourceDeviceType>0</sourceDeviceType>\n      </soap:IBSoapHeader>\n   </soapenv:Header>\n   <soapenv:Body>\n      <end:queryUserIdListReq>\n         <req:userIDs>\n<end:item>\n<sch:ID>{phone_number}</sch:ID>\n<sch:type>0</sch:type>\n</end:item>\n         </req:userIDs>\n      </end:queryUserIdListReq>\n   </soapenv:Body>\n</soapenv:Envelope>'

        jresp, resp = await self._call_api(
            url=url,
            payload=body,
            headers=headers,
            api_name="fake_id",
            subject=phone_number,
        )

        return jresp, resp
        res = {"error_status": False}

        if "success" in resp.lower() and not "failed" in resp.lower():
            try:
                fake_id = jresp["soapenv:Envelope"]["soapenv:Body"][
                    "ns1:queryUserIdListReturn"
                ]["ns5:userIDs"]["ns1:item"]["ns6:ID"]
                res["data"] = fake_id["#text"]
            except:
                res["error_status"] = True

        else:
            res["error_status"] = True

        return res
