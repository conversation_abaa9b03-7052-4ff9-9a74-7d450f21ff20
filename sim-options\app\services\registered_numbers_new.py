from datetime import datetime, timedelta
import asyncio
from pydantic import BaseModel
from motor.motor_asyncio import AsyncIOMotorClient

from utils import persian_text_normalizer
from db.mongo.repository import TerminationRepository
from core.settings import settings


registered_numbers_type_map = {"LT": "Voice", "NORML": "Voice", "TDD": "TDD"}
termination_grace_period = 1
# reported_grace_period = 10
reported_grace_period = 3


class TerminationResult(BaseModel):
    own_number: bool = True
    error_status: bool = False
    nid_mismatch: bool = False
    identity_mismatch: bool = False
    request_error: bool = False


class CancelResult(BaseModel):
    own_number: bool = True
    error_status: bool = False
    final_recipients: list = []


class RegisteredNumbersNewService:
    def __init__(self, eia_client, hsdp_client, aat_client):
        self.eia_client = eia_client
        self.hsdp_client = hsdp_client
        self.aat_client = aat_client

    async def get_customer_info(self, phone_number, fields= None):
        eia_response = await self.eia_client.get_customer_profile(
            phone_number=phone_number
        )
        return eia_response

    async def inquiry(self, session: AsyncIOMotorClient, phone_number):

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        res = await self.eia_client.list_of_subsciber_details(nid, phone_number)
        phone_numbers = []
        result = []
        type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}
        for pn in res:
            if not pn["phone_number"] in phone_numbers and pn["status"] in [
                "Active",
                "Soft-suspended",
                "Suspended",
                "waiting-for-first-callback",
            ]:
                pn["type"] = type_map.get(pn["type"])
                phone_numbers.append(pn["phone_number"])
                msisdn = pn["phone_number"]

                if pn["type"] == "TDD":
                    pn["termination_status"] = "not_eligible"
                else:
                    termination_request = await TerminationRepository(
                        session=session
                    ).find_one_request(msisdn)
                    if termination_request:
                        now = datetime.now().replace(microsecond=0, second=0)
                        termination_request_date = termination_request[
                            "request_datetime"
                        ]
                        if termination_request["status"] == "reported":
                            if (
                                now - termination_request_date
                            # ).days > reported_grace_period:
                            ).total_seconds() / 60 > reported_grace_period:
                                pn["termination_status"] = "not_requested"
                            else:
                                pn["termination_status"] = "pending"
                        else:
                            if (
                                now - termination_request_date
                            ).days < termination_grace_period:
                                pn["termination_status"] = "grace_period"
                            else:
                                pn["termination_status"] = "pending"
                    else:
                        pn["termination_status"] = "not_requested"

                result.append(pn)

        return result

    async def termination_check_number(
        self,
        session: AsyncIOMotorClient,
        phone_number,
        subscriber_phone_number,
        language,
    ):
        res = {
            "error_status": False,
            "termination_possibility": False,
            "reason": "",
            "data": {},
        }

        customer_profile = await self.get_customer_info(
            phone_number=subscriber_phone_number
        )
        sub_nid = customer_profile["national_id"]

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        if sub_nid != nid:
            res["error_status"] = True
            res["reason"] = "failed_due_not_own"
            return res

        query = {
            "msisdn": phone_number,
            "status": "in-progress",
        }
        termination_requests = await TerminationRepository(
            session
        ).get_termination_requests(query)
        if termination_requests:
            res["reason"] = "failed_due_duplication"
            return res

        now = datetime.now()
        last_day = now - timedelta(days=1)
        query = {
            "national_id": nid,
            "request_datetime": {"$gte": last_day, "$lte": now},
            "status": {"$in": ["cancelled", "in-progress"]},
        }
        today_requests = await TerminationRepository(
            session
        ).count_termination_requests(query)
        if today_requests > int(settings.TERMINATION_DAILY_LIMIT):
            res["reason"] = "failed_due_requests"
            return res
        last_week = now - timedelta(days=7)
        query = {
            "national_id": nid,
            "request_datetime": {"$gte": last_week, "$lte": now},
            "status": {"$in": ["cancelled", "in-progress"]},
        }
        this_week_requests = await TerminationRepository(
            session
        ).count_termination_requests(query)
        if this_week_requests > int(settings.TERMINATION_WEEKLY_LIMIT):
            res["reason"] = "failed_due_requests"
            return res
        if customer_profile["profile_type"] != "individual":
            res["reason"] = "failed_due_profile_type"
            return res

        if customer_profile["customer_type"] == "td":
            res["reason"] = "failed_due_sim_type"
            return res

        status = await self.eia_client.suspension_status(phone_number=phone_number)
        if status["error_status"]:
            res["error_status"] = True
            return res
        elif "data" in status:
            if not status["data"]["is_active"]:
                reasons = []
                reasons_res = await self.eia_client.suspension_reasons(
                    phone_number=phone_number
                )
                if not reasons_res["error_status"]:
                    reasons = reasons_res["data"]
                for reason in reasons:
                    reason["description"] = reason["description"][language]
                    reason["guidance"] = reason["guidance"][language]
                res["reason"] = "failed_due_activation"
                res["data"] = {
                    "is_active": status["data"]["is_active"],
                    "reasons": reasons,
                }
                return res
        else:
            res["error_status"] = True
            return res

        shared_account_status = await self.eia_client.shared_account_get_status(
            phone_number=phone_number
        )
        if shared_account_status["error_status"]:
            res["error_status"] = True
            return res
        shared_account_status = shared_account_status["data"]["is_activated"]
        if shared_account_status:
            res["reason"] = "faild_due_sharedaccount"
            return res

        if customer_profile["customer_type"] == "postpaid":
            account_details = await self.eia_client.get_account_details(
                phone_number=phone_number, customer_type="postpaid", language=language
            )
            if (account_details["current_balance"] > 0) or (
                account_details["outstanding_balance"] > 0
            ):
                amount = (
                    account_details["current_balance"]
                    if account_details["current_balance"]
                    else account_details["outstanding_balance"]
                )

                res["reason"] = "faild_due_debt"
                res["data"] = {"debt_type": "bill", "amount": amount}
                return res

        else:
            current_debt = 0

            hsdp_jresp, hsdp_resp = await self.hsdp_client.hsdp_get_fake_id(
                phone_number=phone_number,
            )
            fake_id_res = {"error_status":False}
            if "success" in hsdp_resp.lower() and not "failed" in hsdp_resp.lower():
                try:
                    fake_id = hsdp_jresp["soapenv:Envelope"]["soapenv:Body"][
                        "ns1:queryUserIdListReturn"
                        ]["ns5:userIDs"]["ns1:item"]["ns6:ID"]
                    fake_id_res["data"] = fake_id["#text"]
                except:
                    fake_id_res["error_status"] = True
            else:
                fake_id_res["error_status"] = True

            if not fake_id_res["error_status"] and fake_id_res["data"].startswith("f-"):
                fake_id = fake_id_res["data"]
                aat_res = await self.aat_client.aat_qualification_check(
                    fake_id=fake_id,
                    phone_number=phone_number,
                )

                aat_final_res = {"error_status": False}

                if aat_res["data"]["status_message"] == "success":
                    try:
                        aat_final_res["data"] = {
                            "amount_qualified": aat_res["data"]["amount_qualified"],
                            "loan_balance": aat_res["data"]["loan_balance"],
                            "code": aat_res["data"]["code"],
                        }
                    except:
                        aat_final_res["error_status"] = True
                else:
                    try:
                        aat_final_res["data"] = {
                            "loan_balance": aat_res["data"]["loan_balance"],
                        }
                    except:
                        aat_final_res["error_status"] = True

                if aat_final_res["error_status"]:
                    res["error_status"] = True
                    return res
                
                if not aat_final_res["error_status"] and "loan_balance" in aat_res["data"]:
                    current_debt = aat_res["data"]["loan_balance"]

            if current_debt > 0:
                res["reason"] = "faild_due_debt"
                res["data"] = {"debt_type": "advance_air_time", "amount": current_debt}
                return res
        res["termination_possibility"] = True
        return res

    async def termination_check_entry(
        self, phone_number, national_id, first_name, last_name, father_name
    ):
        res = {
            "error_status": False,
            "termination_possibility": True,
            "reason": "",
        }

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        if customer_profile["national_id"].lower() != national_id.lower():
            res["termination_possibility"] = False
            res["reason"] = "failed_due_nid_mismatch"
            return res

        first_name = persian_text_normalizer(first_name)
        last_name = persian_text_normalizer(last_name)
        father_name = persian_text_normalizer(father_name)
        enterd_first_name = persian_text_normalizer(customer_profile["first_name"])
        enterd_last_name = persian_text_normalizer(customer_profile["last_name"])
        enterd_father_name = persian_text_normalizer(customer_profile["father_name"])

        if (
            first_name != enterd_first_name
            or last_name != enterd_last_name
            or father_name != enterd_father_name
        ):
            res["termination_possibility"] = False
            res["reason"] = "failed_due_identity_mismatch"
            return res

        res["termination_possibility"] = True
        return res

    async def termination_submit(
        self,
        session: AsyncIOMotorClient,
        phone_number,
        national_id,
        subscriber_phone_number,
        first_name,
        last_name,
        father_name,
        reason,
    ):
        customer_profile = await self.get_customer_info(phone_number=phone_number)
        now = datetime.now().replace(microsecond=0)
        query = {
            "msisdn": phone_number,
            "registration_date": customer_profile["registration_date"],
            "profile_type": customer_profile["profile_type"],
            "customer_type": customer_profile["customer_type"],
            "request_datetime": now,
            "national_id": national_id,
            "requested_by": subscriber_phone_number,
            "cancelled_by": "",
            "first_name": first_name,
            "last_name": last_name,
            "father_name": father_name,
            "reason": reason,
            "status": "in-progress",
        }

        await TerminationRepository(session=session).add_termination_request(query)

        res = await self.eia_client.list_of_subsciber_details(national_id, phone_number)

        return res

    async def termination_cancell(
        self, session: AsyncIOMotorClient, phone_number, profile_phone_number
    ):

        cancelled_from_db = await TerminationRepository(
            session
        ).cancell_termination_request(phone_number, profile_phone_number)
        if cancelled_from_db:

            customer_profile = await self.get_customer_info(
                phone_number=phone_number
            )

            try:

                res = await self.eia_client.list_of_subsciber_details(
                    customer_profile["national_id"], phone_number
                )

                final_recipients = []
                own_number = False
                for pn in res:
                    if profile_phone_number == pn["phone_number"]:
                        own_number = True
                    if pn["type"] != "TDD":
                        final_recipients.append(pn["phone_number"])

                if not own_number:
                    return CancelResult(own_number=False)
            except:
                return CancelResult(error_status=True)

            return CancelResult(final_recipients= final_recipients)

    async def delink(self, session: AsyncIOMotorClient, phone_number):
        try:
            await TerminationRepository(session=session).delink_update(phone_number)

            return True
        except:
            return False
