from datetime import datetime
from pydantic import BaseModel
from core.settings import settings
from typing import Optional


class UpgradeResult(BaseModel):
    error_status: bool = False
    outstanding_debt: bool = False
    current_debt: int = 0
    activation_status: bool = True
    is_eligible: Optional[bool] = None
    offer_name: Optional[str] = None
    offer_amount: Optional[float] = None
    code: Optional[str] = None
    amount_qualified: Optional[float] = None
    loan_balance: Optional[float] = None
    offer_id: Optional[str]= None
    offer_credit_limit: Optional[int]= None


class Pre2PostService:
    def __init__(self, hsdp_client, aat_client, eia_client):
        self.hsdp_client = hsdp_client
        self.aat_client = aat_client
        self.eia_client = eia_client

    async def upgrade_pre2post(self, phone_number, fields, language):
        hsdp_jresp, hsdp_resp = await self.hsdp_client.hsdp_get_fake_id(
            phone_number,
        )

        res = {"error_status": False}
        if "success" in hsdp_resp.lower() and not "failed" in hsdp_resp.lower():
            try:
                fake_id = hsdp_jresp["soapenv:Envelope"]["soapenv:Body"][
                    "ns1:queryUserIdListReturn"
                ]["ns5:userIDs"]["ns1:item"]["ns6:ID"]
                res["data"] = fake_id["#text"]
            except:
                res["error_status"] = True
        else:
            res["error_status"] = True

        current_debt = 0
        if not res["error_status"] and res["data"].startswith("f-"):
            fake_id = res["data"]

            aat_res = await self.aat_client.aat_qualification_check(
                fake_id,
                phone_number,
            )

            res = {"error_status": False}

            if aat_res["data"]["status_message"] == "success":
                try:
                    res["data"] = {
                        "amount_qualified": aat_res["data"]["amount_qualified"],
                        "loan_balance": aat_res["data"]["loan_balance"],
                        "code": aat_res["data"]["code"],
                    }
                except:
                    return UpgradeResult(error_status=True)
            else:
                try:
                    res["data"] = {
                        "loan_balance": aat_res["data"]["loan_balance"],
                    }
                except:
                    return UpgradeResult(error_status=True)

            if not res["error_status"] and "loan_balance" in res["data"]:
                current_debt = res["data"]["loan_balance"]
                if int(current_debt) > 5000:
                    return UpgradeResult(outstanding_debt=True, current_debt=0)

        customer_profile = await self.eia_client.get_customer_profile(
            phone_number, fields
        )
        if customer_profile["operation_status"] not in ["active"]:
            return UpgradeResult(activation_status=False)

        res = await self.eia_client.pre2post(phone_number)
        if res["error_status"]:
            return UpgradeResult(error_status=True)

        final_res = res["data"]
        final_res["offer_name"] = (
            res["data"]["offer_name"][language]
            if not res["data"]["offer_name"][language] is None
            else res["data"]["offer_name"]["name"]
        )

        now = datetime.now()
        vat_changetime = datetime.strptime(
            settings.VAT_CHANGETIME, "%Y-%m-%dT%H:%M:%SZ"
        )
        if now < vat_changetime:
            VAT_MARGIN = int(settings.VAT_PREVIOUS)
        else:
            VAT_MARGIN = int(settings.VAT_CURRENT)

        final_res["offer_amount"] = (
            final_res["offer_amount"] * (100 + VAT_MARGIN) / 100
        ) // 1
        final_res["is_eligible"] = True
        return UpgradeResult(**final_res)
