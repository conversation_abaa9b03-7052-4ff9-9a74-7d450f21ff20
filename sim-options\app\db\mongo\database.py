from core.settings import settings
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient, uri_parser


class DataBase:
    client: MongoClient = None
    parameters: dict = None


db = DataBase()


async def get_database() -> MongoClient:
    return db.client[db.parameters["database"]]


async def connect_to_db():
    db.parameters = uri_parser.parse_uri(settings.MONGODB_URL)
    db.client = AsyncIOMotorClient(settings.MONGODB_URL)
    await db.client[db.parameters["database"]].get_collection(
        "termination_requests"
    ).create_index([("msisdn", 1), ("status", 1)])
    await db.client[db.parameters["database"]].get_collection("dordaneh").create_index(
        [("msisdn", 1), ("dordaneh_msisdn", 1)]
    )
    await db.client[db.parameters["database"]].get_collection("dordaneh").create_index(
        [("msisdn", 1)]
    )


async def close_db_connection():
    db.client.close()
