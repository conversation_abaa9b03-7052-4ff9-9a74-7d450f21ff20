from datetime import datetime
import asyncio
from pydantic import BaseModel
from utils import termination_check_entry


registered_numbers_type_map = {"LT": "Voice", "NORML": "Voice", "TDD": "TDD"}


class TerminationResult(BaseModel):
    own_number: bool = True
    error_status: bool = False
    nid_mismatch: bool = False
    identity_mismatch: bool = False
    request_error: bool = False


class CancelResult(BaseModel):
    own_number: bool = True
    error_status: bool = False


class RegisteredNumbersService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def get_customer_info(self, phone_number, fields= None):
        eia_response = await self.eia_client.get_customer_profile(
            phone_number=phone_number
        )
        return eia_response

    async def inquiry(self, phone_number):
        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        subscriber_details = await self.eia_client.list_of_subsciber_details(
            nid, phone_number
        )
        phone_numbers = []
        res = []
        for subscriber_detail in subscriber_details:
            if not subscriber_detail[
                "phone_number"
            ] in phone_numbers and subscriber_detail["status"] in [
                "Active",
                "Soft-suspended",
                "Suspended",
                "waiting-for-first-callback",
            ]:
                phone_numbers.append(subscriber_detail["phone_number"])
                res.append(
                    {
                        "phone_number": subscriber_detail["phone_number"],
                        "status": subscriber_detail["status"],
                        "type": registered_numbers_type_map.get(
                            subscriber_detail["type"], ""
                        ),
                    }
                )

        return res

    async def get_status(self, phone_number):

        customer_profile = await self.get_customer_info(phone_number=phone_number)
        nid = customer_profile["national_id"]

        subscriber_details = await self.eia_client.list_of_subsciber_details(
            nid, phone_number
        )
        phone_numbers = []
        for subsciber_detail in subscriber_details:
            if (
                not subsciber_detail["phone_number"] in phone_numbers
                and subsciber_detail["status"]
                in [
                    "Active",
                    "Soft-suspended",
                    "Suspended",
                    "waiting-for-first-callback",
                ]
                and subsciber_detail["type"] != "TDD"
            ):
                phone_numbers.append(subsciber_detail["phone_number"])

        tasks = []
        for phone_number in phone_numbers:
            tasks.append(
                self.eia_client.pending_online_termination_request(phone_number)
            )

        res = []
        tasks_result = await asyncio.gather(*tasks, return_exceptions=True)
        for task_result in tasks_result:
            if not isinstance(task_result, Exception):
                if not task_result["error_status"]:
                    data = task_result["data"]
                    res.append(
                        {
                            "phone_number": data["phone_number"],
                            "termination_status": data["termination_status"],
                        }
                    )

        return res

    async def termination_request(
        self,
        subscriber_phone_number,
        phone_number,
        national_id,
        first_name,
        last_name,
        father_name,
    ):
        subscribers_registered_numbers = (
            await self.eia_client.list_of_subsciber_details(
                national_id, subscriber_phone_number
            )
        )

        own_number = False
        for registered_number in subscribers_registered_numbers:
            if phone_number == registered_number["phone_number"]:
                own_number = True

        if not own_number:
            return TerminationResult(own_number=False)

        termination_check_status = await termination_check_entry(
            phone_number, national_id, first_name, last_name, father_name
        )
        if termination_check_status["error_status"]:
            return TerminationResult(error_status=False)
        if not termination_check_status["termination_possibility"]:
            if termination_check_status["reason"] == "failed_due_nid_mismatch":
                return TerminationResult(nid_mismatch=True)
            if termination_check_status["reason"] == "failed_due_identity_mismatch":
                return TerminationResult(identity_mismatch=True)
        else:
            res = await self.eia_client.online_termination_request(
                subscriber_code=subscriber_phone_number, phone_number=phone_number
            )

            if res["error_status"]:
                return TerminationResult(request_error=True)
        return TerminationResult()

    async def cancel_termination(self, subscriber_phone_number, phone_number):
        customer_profile = await self.get_customer_info(
            phone_number=subscriber_phone_number
        )
        nid = customer_profile["national_id"]

        subscribers_registered_numbers = (
            await self.eia_client.list_of_subsciber_details(
                nid, subscriber_phone_number
            )
        )

        own_number = False
        for registered_number in subscribers_registered_numbers:
            if phone_number == registered_number["phone_number"]:
                own_number = True

        if not own_number:
            return CancelResult(own_number=False)

        res = await self.eia_client.cancel_online_termination(
            subscriber=subscriber_phone_number, phone_number=phone_number
        )

        if res["error_status"]:
            return CancelResult(error_status=True)

        return CancelResult()
