from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends
from core.config import authorization, ngpg_client, eia_client
from swagger import transactions_history_swagger

from ngmi_logging.utils import add_params_into_access_log

from services import TransactionService
from core.config import get_transaction_service

router = APIRouter()


@router.get(
    "/transactions_history",
    responses=transactions_history_swagger.transactions_history_sample_resonses,
)
async def transactions_history(
    transaction_service: Annotated[
        TransactionService, Depends(get_transaction_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber transaction history
    - **status**: Defines internet usage status

    Raises:

        401: If JWT token is invalid

    Returns:

        message: Represents operation status

    """
    add_params_into_access_log(key="target", value="click")
    add_params_into_access_log(key="source", value="fav_transaction")
    add_params_into_access_log(key="media", value="click")

    res = await transaction_service.transaction_history(
        phone_number=profile["phone_number"],
        cow_date=profile["cow_date"],
        client_name=profile["client_name"],
        sim_type=profile["sim_type"],
    )

    return res


@router.get(
    "/transactions_history/gifts",
    responses=transactions_history_swagger.transactions_history_gifts_sample_responses,
)
async def gift_transactions_history(
    transaction_service: Annotated[
        TransactionService, Depends(get_transaction_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber transaction history
    - **status**: Defines internet usage status

    Raises:

        401: If JWT token is invalid

    Returns:

        transaction_id: represents payment transaction id,
        offer_title: represents gifted offer title,
        service: represents payment service,
        amount: represents payment amount,
        payment_mode: represents payment mode,
        date: represents payment date,
        payment_status: represents payment status,,
        purchase_status: represents payment fullfilment status,

    """
    phone_number = profile["phone_number"]
    language = profile["language"]
    cow_date = profile["cow_date"]
    client_name = profile["client_name"]

    res = await transaction_service.gift_history(
        phone_number=phone_number,
        language=language,
        cow_date=cow_date,
        client_name=client_name,
    )

    return res
