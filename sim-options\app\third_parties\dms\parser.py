import os
from pathlib import Path
from lxml import etree


class XML:
    def __init__(self):
        self.base_path = Path(__file__).resolve()
        self.xml_files_path = self.base_path.parent / "files"
        self.xml_tree_cache = {}

    def _get_all_namespaces(self, root):
        all_namespaces = {}
        for element in root.iter():
            if element.nsmap:
                for prefix, uri in element.nsmap.items():
                    if prefix not in all_namespaces and prefix:
                        all_namespaces[prefix] = uri
        return all_namespaces

    def _update_tree(self, root, **kwargs):
        namespaces = self._get_all_namespaces(root=root)
        for key, value in kwargs.items():
            xpath = f"//*[local-name() = '{key}']"
            element = root.xpath(xpath, namespaces=namespaces)
            if element:
                element[0].text = str(value)

    def dms_parser(self, xml_filename: str, **kwargs):
        xml_filename = xml_filename.encode("utf-8")
        root = etree.fromstring(xml_filename)
        self._update_tree(root, **kwargs)
        return etree.tostring(
            root, pretty_print=True, encoding="UTF-8", xml_declaration=True
        ).decode()

    def dms_request_parser(self, header=None, body=None):
        if header and header.get("Authorization"):
            header["Authorization"] = "****"
        body = self.dms_parser(body, password="****")
        return header, body
