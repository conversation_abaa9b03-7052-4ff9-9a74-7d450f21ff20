from typing import Any, Annotated
import asyncio
from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse, Response
from core.config import authorization, notification_client, eia_client
from schemas import (
    RegisteredNumbersInput,
    TerminationOtpRequestInput,
    TerminationSubmit,
    CancelTerminationRequestInput,
)
from utils import termination_check_entry
from swagger import registered_numbers_new

from services import RegisteredNumbersService
from core.config import get_registered_numbers_service


router = APIRouter()

registered_numbers_type_map = {"LT": "Voice", "NORML": "Voice", "TDD": "TDD"}


@router.post(
    "/otp/request", responses=registered_numbers_new.otp_request_sample_responses
)
async def request_registered_numbers_otp(
    request: Request,
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Request for otp with intention of registered_numbers_inquiry
    Raises:
        400: too many otp request
    Returns:
        None
    """

    phone_number = profile["phone_number"]

    language = profile["language"]

    res = await notification_client.send_sms_otp(
        phone_number=phone_number,
        language=language,
        intention="registered_numbers_inquiry",
        client_id=profile["client_id"],
        # sim_type=profile["sim_type"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post("/inquiry", responses=registered_numbers_new.inquiry_sample_responses)
async def registered_numbers(
    request: Request,
    otp_input: RegisteredNumbersInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersService, Depends(get_registered_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber registered MSISDNs

    Raises:

        401: If JWT token is invalid

    Returns:

        200: List of Subscriber's registered numbers

    """
    otp_input = otp_input.model_dump()
    otp = otp_input["otp"]
    recipient = otp_input["recipient"]

    phone_number = profile["phone_number"]

    res = await notification_client.validate_otp(
        recipient=recipient,
        otp=otp,
        intention="registered_numbers_inquiry",
        originated_msisdn=phone_number,
    )

    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    result = await registered_numbers_service.inquiry(phone_number=phone_number)
    return result


@router.get("/status", responses=registered_numbers_new.status_sample_responses)
async def status(
    registered_numbers_service: Annotated[
        RegisteredNumbersService, Depends(get_registered_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Check termination possibility for a given phone number
    Raises:
        400:
            If profile type is cooperate
        401: If JWT token is invalid

    Returns:
            None
    """
    result = await registered_numbers_service.get_status(
        phone_number=profile["phone_number"]
    )
    return result


@router.post(
    "/termination/otp/request",
    responses=registered_numbers_new.termination_otp_request_sample_response,
)
async def termination_otp_request(
    request: Request,
    termination_otp: TerminationOtpRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersService, Depends(get_registered_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Request for otp with intention of termination
    Raises:

        400: too many otp request

    Returns:

        None

    """
    termination_otp = termination_otp.model_dump()
    phone_number = termination_otp["phone_number"]
    national_id = termination_otp["national_id"]
    first_name = termination_otp["first_name"]
    last_name = termination_otp["last_name"]
    father_name = termination_otp["father_name"]

    profile_phone_number = profile["phone_number"]
    language = profile["language"]

    termination_check_status = await termination_check_entry(
        phone_number,
        national_id,
        first_name,
        last_name,
        father_name,
    )

    if termination_check_status["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if not termination_check_status["termination_possibility"]:
        if termination_check_status["reason"] == "failed_due_nid_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
                    "title": "Online termination operation failed due nid mismatch",
                },
            )

        if termination_check_status["reason"] == "failed_due_identity_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
                    "title": "Online termination operation failed due identity mismatch",
                },
            )

    res = await notification_client.send_sms_otp(
        phone_number=profile_phone_number,
        language=language,
        intention=f"termination-{phone_number}",
        client_id=profile["client_id"],
        # sim_type=profile["sim_type"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post(
    "/terminate/request",
    responses=registered_numbers_new.terminate_request_sample_response,
)
async def termination_submit_request(
    request: Request,
    request_input: TerminationSubmit,
    registered_numbers_service: Annotated[
        RegisteredNumbersService, Depends(get_registered_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Submit a termination request for a given phone number
    Raises:
        400:
            If profile type is cooperate
            If there are more than 3 requests on the same day
            If the entered The national is incorrect
            If the phone number is not active
            If there is an active shared account
            If there is outstanding debt

        401: If JWT token is invalid
    Returns:
            None

    """
    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    national_id = request_input["national_id"]
    first_name = request_input["first_name"]
    last_name = request_input["last_name"]
    father_name = request_input["father_name"]
    otp = request_input["otp"]
    recipient = request_input["recipient"]

    subscriber_phone_number = profile["phone_number"]
    subscriber_customer_type = profile["customer_type"]

    res = await notification_client.validate_otp(
        recipient=recipient,
        otp=otp,
        intention=f"termination-{phone_number}",
        originated_msisdn=subscriber_phone_number,
    )

    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    result = await registered_numbers_service.termination_request(
        subscriber_phone_number=profile["phone_number"],
        phone_number=request_input["phone_number"],
        national_id=request_input["national_id"],
        first_name=request_input["first_name"],
        last_name=request_input["last_name"],
        father_name=request_input["father_name"],
    )

    if result.error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if result.nid_mismatch:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
                "title": "Online termination operation failed due nid mismatch",
            },
        )

    if result.identity_mismatch:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
                "title": "Online termination operation failed due identity mismatch",
            },
        )

    if result.request_error:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/termination/request/failed",
                "title": "Online termination operation failed",
            },
        )

    return Response(status_code=200)


@router.post(
    "/terminate/cancel",
    responses=registered_numbers_new.terminate_cancel_sample_responses,
)
async def cancel_terminate_request(
    request: Request,
    request_input: CancelTerminationRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersService, Depends(get_registered_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Cancel the termination request for a given phone number

    Raises:

        401: If JWT token is invalid

    Returns:

        None

    """
    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    subscriber_phone_number = profile["phone_number"]

    result = await registered_numbers_service.cancel_termination(
        subscriber_phone_number=subscriber_phone_number, phone_number=phone_number
    )

    if not result.own_number:
        return Response(status_code=400, content= "failed_due_not_own")

    if result.error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/termination/request/failed",
                "title": "Online termination operation failed",
            },
        )

    return Response(status_code=200)
