from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime, timedelta
import uuid
from pydantic import BaseModel

from db.mongo.repository import TerminationRepository


class DocumentResult(BaseModel):
    invalid_page: bool = False


class TerminationService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def generate_termination_report(
        self,
        session: AsyncIOMotorClient,
        termination_grace_period,
        status,
    ):
        end_date = datetime.now().replace(
            microsecond=0, second=0, minute=0, hour=0
        ) - timedelta(days=termination_grace_period)

        batch_id = str(uuid.uuid4())
        result = await TerminationRepository(session=session).add_batch_id(
            end_date, status, batch_id
        )

        if result["updated_documents"] == 0:
            pass

        return {"batch_id": batch_id, "result": result}

    async def remove_batch(self, session: AsyncIOMotorClient, batch_id, status):

        result = await TerminationRepository(session=session).remove_batch_id(
            status, batch_id
        )
        return {
            "message": "Batch ID removed successfully",
            "result": {"updated_documents": result.modified_count},
        }

    async def documents(self, session: AsyncIOMotorClient, batch_id, page_size, page):

        total_documents = await TerminationRepository(session=session).count_bach_id(
            batch_id
        )

        total_pages = (total_documents + page_size - 1) // page_size

        if page > total_pages:
            return DocumentResult(invalid_page=True)

        skip = (page - 1) * page_size

        documents = await TerminationRepository(
            session=session
        ).find_docs_with_pagination(batch_id, skip, page_size)

        return {
            "documents": documents,
            "total_pages": total_pages,
            "total_documents": total_documents,
        }
