from datetime import <PERSON><PERSON><PERSON>
from uuid import uuid4
from datetime import datetime
import copy

from fastapi import HTTPException


from ngmi_http import HTTPBase, InvalidUpstreamResponse

from .ngpg_services import services
from .ngpg_code_map import ngpg_code_map
from .parser import ngpg_parser

from typing import List, Dict, Optional
from pydantic import BaseModel, Field


class GiftTransactionsResponse(BaseModel):
    offers: Dict[str, dict]
    transactions: Optional[List[dict]] = Field(default=None)


class TransactionsResponse(BaseModel):
    transactions: Optional[List[dict]] = Field(default=None)


class NGPGClient(HTTPBase):
    def __init__(
        self,
        base_url,
        token,
        mid,
        ngpg_channel_app,
        ngpg_channel_web,
        offer_map,
        logger,
        http_client=None,
    ) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.token = token
        self.mid = mid
        self.ngpg_channel_app = ngpg_channel_app
        self.ngpg_channel_web = ngpg_channel_web
        self.offer_map = offer_map

    @staticmethod
    def get_valid_msisdn(phone_number):
        if len(phone_number) == 10 and phone_number.startswith("9"):
            return f"98{phone_number}"
        elif len(phone_number) == 11 and phone_number.startswith("0"):
            return f"98{phone_number[1:]}"
        elif len(phone_number) == 12 and phone_number.startswith("989"):
            return phone_number
        else:
            raise ValueError("Invalid msisdn")

    async def _call_api(
        self,
        url,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        timeout=None,
        request_parser=None,
    ):
        if not request_parser:
            request_parser = ngpg_parser

        if not headers:
            headers = {}
        if not params:
            params = {}
        if not body:
            body = {}
        url = self.base_url + url

        headers["Authentication"] = self.token
        headers["X-MID"] = self.mid
        headers["Accept"] = "application/json"
        if not request_parser:
            request_parser = ngpg_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            timeout=timeout,
            api_name=api_name,
            subject=subject,
            service="NGPG",
            request_parser=request_parser,
        )

        return response

    async def get_payment_modes(
        self,
        phone_number,
        service: str,
        amount: int,
        channel_name: str,
        offer_code: str = "",
        language: str = "fa",
        beneficiary_phone_number: str = None,
        data_counter: str = None,
        voice_counter: str = None,
        sms_counter: str = None,
        validity_duration: str = None,
        increased_cl_amount: int = None,
        fttx_id: str = None,
        offer_desc: str = None,
    ):

        if service not in services:
            raise ValueError(f"Invalid NGPG Service {service}")

        url = "/getPaymentMode"
        body = {
            "channel": channel_name,
            "orderId": uuid4().hex,
            "requestTime": datetime.now().strftime("%d%m%Y %H:%M:%S:%f"),
            "mobileNumber": self.get_valid_msisdn(phone_number),
            "mid": self.mid,
            "amount": amount,
            "service": service,
            "offerCode": offer_code,
            "parameters": {
                "parameter": [{"key": "is_bank_list_required", "value": True}]
            },
        }
        subject = phone_number

        if service != "SharedAccConsumerAdd" and beneficiary_phone_number:
            body["parameters"]["parameter"].append(
                {
                    "key": "BMobileNumber",
                    "value": self.get_valid_msisdn(beneficiary_phone_number),
                }
            )

        if service in [
            "NormalBolton",
            "GiftBolton",
            "DTSOffer",
            "OnlineBuyable",
            "BuyyableOffer",
            "SharedBolton",
        ]:
            if not offer_desc:
                raise ValueError("Insufficient parameters")

            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )

        elif service == "FTTHBolton":
            if not offer_desc or not fttx_id:
                raise ValueError("Insufficient parameters")

            subject = fttx_id
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )
            body["parameters"]["parameter"].append({"key": "ftth_id", "value": fttx_id})
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )
            body["parameters"]["parameter"].append(
                {"key": "changeOfferFlag", "value": "True"}
            )
            body["parameters"]["parameter"].append(
                {
                    "key": "InitialOfferFlag",
                    "value": "False",
                }
            )
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_code}
            )

        elif service in ["DynamicBolton", "MPDynamicBolton"]:
            if not (data_counter or sms_counter or voice_counter or validity_duration):
                raise ValueError("Insufficient parameters")
            body["parameters"]["parameter"].append(
                {"key": "data_counter", "value": data_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "sms_counter", "value": sms_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "voice_counter", "value": voice_counter}
            )

            body["parameters"]["parameter"].append(
                {"key": "no_of_days", "value": validity_duration}
            )

        elif service == "SharedAccConsumerAdd":
            if not beneficiary_phone_number:
                raise ValueError("Insufficient parameters")

            offer_descs = {"fa": "افزودن زیرشاخه", "en": "adding consumer"}
            body["parameters"]["parameter"].append(
                {"key": "providerMSISDN", "value": self.get_valid_msisdn(phone_number)}
            )
            body["parameters"]["parameter"].append(
                {
                    "key": "consumerMSISDN",
                    "value": self.get_valid_msisdn(beneficiary_phone_number),
                }
            )
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_descs[language]}
            )
            body["parameters"]["parameter"].append(
                {"key": "languageId", "value": "F" if language == "fa" else "E"}
            )

        elif service == "Pre2PostMigration":
            if not increased_cl_amount:
                raise ValueError("Insufficient parameters")

            body["parameters"]["parameter"].append(
                {"key": "increased_amount", "value": increased_cl_amount}
            )
            body["parameters"]["parameter"].append({"key": "paymentType", "value": "F"})

        elif service == "WalletCashIn":
            offer_desc = "شارژ " + str(amount) + "ریالی"
            body["parameters"]["parameter"].append(
                {"key": "offerDesc", "value": offer_desc}
            )
            body["parameters"]["parameter"].append({"key": "languageId", "value": "F"})

        response = await self._call_api(
            url=url,
            method="POST",
            body=body,
            api_name="getPaymentMode",
            subject=subject,
        )
        if response["status_code"] == 200:
            try:
                data = response["data"]
                return data
            except:
                raise InvalidUpstreamResponse
        else:
            raise InvalidUpstreamResponse

    async def capture_payment(
        self,
        phone_number,
        service: str,
        amount: int,
        channel_name: str,
        order_id: str,
        reference_id: str,
        payment_mode_id: str,
        callback_url: str,
        offer_code: str,
        language: str = "fa",
        bank_id: str = None,
        auto_renew: bool = False,
    ):

        if service not in services:
            raise ValueError(f"Invalid NGPG Service {service}")

        url = "/capturePayment"
        body = {
            "channel": channel_name,
            "orderId": order_id,
            "requestTime": datetime.now().strftime("%d%m%Y %H:%M:%S:%f"),
            "mobileNumber": self.get_valid_msisdn(phone_number),
            "amount": amount,
            "requestType": "NSEAM",
            "referenceId": reference_id,
            "loyaltyPointtoRedeem": "",
            "mid": self.mid,
            "paymentModeId": payment_mode_id,
            "service": service,
            "offerCode": offer_code,
            "callBackUrl": callback_url,
            "languageId": "F" if language == "fa" else "E",
            "parameters": {"parameter": []},
        }
        subject = phone_number

        if payment_mode_id == "BA":
            body["parameters"] = {"parameter": [{"key": "bankId", "value": bank_id}]}
        if auto_renew:
            body["parameters"]["parameter"].append(
                {"key": "autoRenewFlag", "value": "Y"}
            )

        response = await self._call_api(
            url=url,
            method="POST",
            body=body,
            api_name="capturePayment",
            subject=subject,
        )
        if response["status_code"] == 200:
            try:
                data = response["data"]
                return data
            except:
                raise InvalidUpstreamResponse
        else:
            raise InvalidUpstreamResponse

    async def get_gift_transactions_history(
        self,
        phone_number,
        language,
        cow_date,
        channel,
    ) -> GiftTransactionsResponse:
        headers = {
            "Content-Type": "application/json",
        }

        today = datetime.now()

        ngpg_channel = self.ngpg_channel_app
        if channel == "web":
            ngpg_channel = self.ngpg_channel_web

        start_date = today - timedelta(days=30)
        if cow_date:
            cow_datetime = datetime.strptime(cow_date, "%Y-%m-%d")
            if cow_datetime > start_date:
                start_date = (
                    cow_datetime + timedelta(days=1)
                    if cow_datetime.day <= today.day
                    else cow_datetime
                )

        if start_date > today:
            return []

        start_date = start_date.strftime("%Y-%m-%d")
        end_date = today.strftime("%Y-%m-%d")
        body = {
            "limit": 1000,
            "startDate": start_date,
            "msisdn": phone_number[2:],
            "endDate": end_date,
            "service": "GiftBolton",
            "channel": ngpg_channel,
        }
        url = "/transactionHistory"

        response = await self._call_api(
            url=url,
            method="POST",
            api_name="transactionHistory",
            subject=phone_number,
            body=body,
            headers=headers,
        )

        if response["status_code"] != 200 or response["data"]["resultCode"] != "0":
            raise InvalidUpstreamResponse

        offers = copy.deepcopy(self.offer_map)
        transaction_list = response["data"]["transactionList"]

        return GiftTransactionsResponse(offers=offers, transactions=transaction_list)

        offers = copy.deepcopy(self.offer_map)

        res = []
        if response["data"]["transactionList"]:
            for payment in response["data"]["transactionList"]:
                try:
                    offer = offers[payment["offerCode"]]
                    offer_title = (
                        offer["fa_title"] if language == "fa" else offer["en_title"]
                    )
                except KeyError:
                    offer_title = payment["offerCode"]
                d = {
                    "transaction_id": payment["ipsReferenceId"],
                    "offer_title": offer_title,
                    "service": payment["service"],
                    "amount": payment["paidAmount"],
                    "payment_mode": payment["paymentMode"],
                    "date": payment["transactionDate"],
                    "beneficiary_phone_number": payment.get("serviceMsisdn"),
                    "payment_status": (
                        True if payment["paymentStatus"] == "S" else False
                    ),
                    "purchase_status": (
                        True
                        if payment["fulfillmentReferenceId"]
                        and payment["paymentStatus"] == "S"
                        else False
                    ),
                }
                res.append(d)
        return res

    async def get_transactions_history(
        self, phone_number, cow_date, channel, sim_type="fd"
    ) -> TransactionsResponse:
        headers = {
            "Content-Type": "application/json",
        }

        today = datetime.now()

        ngpg_channel = self.ngpg_channel_app
        if channel == "web":
            ngpg_channel = self.ngpg_channel_web

        start_date = today - timedelta(days=30)
        if cow_date:
            cow_datetime = datetime.strptime(cow_date, "%Y-%m-%d")
            if cow_datetime > start_date:
                start_date = (
                    cow_datetime + timedelta(days=1)
                    if cow_datetime.day <= today.day
                    else cow_datetime
                )

        if start_date > today:
            return []

        end_date = today.strftime("%Y-%m-%d")
        start_date = start_date.strftime("%Y-%m-%d")

        body = {
            "limit": 1000,
            "startDate": start_date,
            "msisdn": phone_number[2:],
            "endDate": end_date,
            "channel": ngpg_channel,
        }

        if sim_type == "fttx":
            body["service"] = "FTTHBolton"

        url = "/v2/transactionHistory"

        response = await self._call_api(
            url=url,
            method="POST",
            api_name="transactionHistory",
            body=body,
            headers=headers,
            subject=phone_number,
        )

        if response["status_code"] != 200 or response["data"]["resultCode"] != "0":
            raise InvalidUpstreamResponse

        transaction_list = response["data"]["transactionList"]
        return TransactionsResponse(transactions=transaction_list)

        res = []
        if response["data"]["transactionList"]:
            for payment in response["data"]["transactionList"]:
                purchase_status = None
                if (
                    payment["service"] in ["WalletCashIn", "BNPLPayBack"]
                    and payment["paymentStatus"] == "S"
                ):
                    purchase_status = True
                d = {
                    "transaction_id": payment["ipsReferenceId"],
                    "service": payment["service"],
                    "amount": payment["paidAmount"],
                    "bank_name": payment.get("bankName"),
                    "bank_reference_id": payment.get("bankReferenceId"),
                    "payment_mode": payment["paymentMode"],
                    "date": payment["transactionDate"],
                    "payment_status": (
                        True if payment["paymentStatus"] == "S" else False
                    ),
                    "purchase_status": (
                        True
                        if purchase_status
                        or (
                            payment["fulfillmentReferenceId"]
                            and payment["paymentStatus"] == "S"
                            and purchase_status is None
                        )
                        else False
                    ),
                    "service_msisdn": payment["serviceMsisdn"],
                    "beneficiaryPhoneNumber": payment["serviceMsisdn"],
                    "offer_code": payment["offerCode"],
                }

                component_li = []
                if payment.get("paymentComponentList", ""):
                    for component in payment["paymentComponentList"]:
                        component_dictionary = {
                            "payment_mode": component["paymentMode"],
                            "paid_amount": component["paidAmount"],
                        }

                        component_li.append(component_dictionary)

                d["payment_transactions"] = component_li

                res.append(d)
        return res
