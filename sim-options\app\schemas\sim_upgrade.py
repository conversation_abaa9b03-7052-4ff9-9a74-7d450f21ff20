from pydantic import BaseModel
from typing import Optional


class SimUpgradeWBPInput(BaseModel):
    otp: str
    wbp_kit_number: str
    wbp_puk: str


class PostalValidationInput(BaseModel):
    postal_code: str
    phone_number: Optional[str] = None


class UpgradePostalRequestInput(BaseModel):
    otp: str
    first_name: str
    last_name: str
    province: str
    postal_code: str
    city: str
    address: str
    house_number: int
    unit: int
    contact_number: str
