from pydantic import BaseModel, field_validator
from typing import List


class TerminationOtpRequestInput(BaseModel):
    phone_number: str
    national_id: str
    first_name: str
    last_name: str
    father_name: str
    reason: str

    @field_validator("phone_number")
    def phone_number_valid(cls, v):
        if len(v) != 12 or not v.startswith("98"):
            raise ValueError("Phone number is invalid")
        return v

    @field_validator("reason")
    def grant_type_valid(cls, v):
        if v not in ["unwanted", "unaware"]:
            raise ValueError("Grant type is invalid")
        return v


class TerminationSubmit(BaseModel):
    phone_number: str
    national_id: str
    first_name: str
    last_name: str
    father_name: str
    reason: str
    otp: str
    recipient: str

    @field_validator("phone_number")
    def phone_number_valid(cls, v):
        if len(v) != 12 or not v.startswith("98"):
            raise ValueError("Phone number is invalid")
        return v

    @field_validator("reason")
    def grant_type_valid(cls, v):
        if v not in ["unwanted", "unaware"]:
            raise ValueError("Reason is invalid")
        return v


class CancelTerminationRequestInput(BaseModel):
    phone_number: str

    @field_validator("phone_number")
    def phone_number_valid(cls, v):
        if len(v) != 12 or not v.startswith("98"):
            raise ValueError("Phone number is invalid")
        return v


class ChangeTerminationStatus(BaseModel):
    phone_numbers: List[str]
