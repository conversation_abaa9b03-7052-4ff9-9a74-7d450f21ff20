get_notification_preferences_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "language": "fa",
        "sms_phone_number": "989999999999",
        "email_address": "<EMAIL>",
        "email_notification": True,
        "push_notification": True,
        "sms_notification": False,
    },
}

patch_notification_preferences_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "required OTP",
        "content": {
            "application/json": {
                "example": [
                    {"SMS OTP is required."},
                    {
                        "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                        "title": "OTP is invalid",
                    },
                ]
            }
        },
    },
    200: {
        "description": "done",
        "content": {"application/json": {"example": {"message": "done"}}},
    },
}

otp_request_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Operation Failed",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/login/otp_request/too_many",
                    "title": "Too many OTP attempts in time window",
                    "detail": "You can only request for OTP once in 120 seconds. You can try in 107 seconds",
                    "params": {"phone_number": "989039038867", "barred_for": "107"},
                }
            }
        },
    },
}
