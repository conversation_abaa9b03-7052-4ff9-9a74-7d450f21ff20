import csv
import os


def parse_offer_mapping():
    absolute_path = os.path.dirname(os.path.abspath(__file__))
    rows = csv.DictReader((open(f"{absolute_path}/sim_offers.csv", encoding="utf-8")))
    res = {}

    for row in rows:
        offer_type = row["offer_type"].lower()
        if offer_type == "internet":
            offer_type = "data"

        offer_id = row["offer_id"]
        upc_code = row["upc_code"]

        i = {
            "offer_id": offer_id,
            "offer_type": offer_type,
            "fa_title": row["fa_title"],
            "en_title": row["en_title"],
            "upc_code": upc_code,
        }
        res[upc_code] = i

    return res
