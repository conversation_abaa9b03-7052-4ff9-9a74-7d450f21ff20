from datetime import datetime
from pydantic import BaseModel


class SimUpgradeResult(BaseModel):
    is_eligible_for_upgrade: bool = True


class SimUpgradeService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def get_customer_info(self, phone_number, fields=None):
        eia_response = await self.eia_client.get_customer_profile(phone_number, fields)
        return eia_response

    async def sim_upgrade_eligibility(self, phone_number, fields, customer_type):
        is_eligible_for_upgrade = True

        sim_category = await self.eia_client.get_customer_profile(phone_number, fields)

        if sim_category["sim_category"] == "4g":
            is_eligible_for_upgrade = False

        suspension = await self.eia_client.get_line_status(phone_number, customer_type)

        if suspension["suspension_status"]:
            is_eligible_for_upgrade = False

        return SimUpgradeResult(is_eligible_for_upgrade=is_eligible_for_upgrade)

    async def sim_upgrade_validate_postal_code(self, phone_number, postal_code):
        eia_response = await self.eia_client.sim_upgrade_validate_postal_code(
            phone_number, postal_code
        )
        return eia_response
