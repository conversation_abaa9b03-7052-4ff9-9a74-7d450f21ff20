from datetime import datetime
from core.config import bom_client
import asyncio


class TtarifPlanService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def get_tarif(self, phone_number, customer_type, language, fields=None):
        tasks = [
            self.eia_client.get_account_details(
                phone_number=phone_number,
                customer_type=customer_type,
                language=language,
                fields=["current_tariff_plan"],
            ),
            self.eia_client.get_eligible_tariff_plans(
                phone_number=phone_number, customer_type=customer_type, lang=language
            ),
        ]
        data = await asyncio.gather(*tasks)
        res = data[0] | data[1]
        return res

    async def change_plan(self, phone_number, customer_type, lang, tariff_code):
        eia_response = await self.eia_client.change_tariff_plan(
            phone_number=phone_number,
            customer_type=customer_type,
            lang=lang,
            tariff_code=tariff_code,
        )
        return eia_response

    async def get_history(self, phone_number, lang, cow_date):
        res = await self.eia_client.get_package_change_history(phone_number, lang)

        result = []
        if cow_date:
            cow_date = datetime.strptime(cow_date, "%Y-%m-%d")
            for item in res:
                if item["date"] != "" and item["date"] is not None:
                    if cow_date <= datetime.strptime(
                        item["date"].split()[0], "%Y-%m-%d"
                    ):
                        result.append(item)
        else:
            result = res

        result = [res_out for res_out in result if res_out["date"] != ""]

        return result
