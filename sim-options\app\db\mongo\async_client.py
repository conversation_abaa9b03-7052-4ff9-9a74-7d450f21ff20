import pymongo
from motor.motor_asyncio import AsyncIOMotorClient
from typing import List


class MongoDBAsyncClient:
    client: pymongo.MongoClient = None
    parameters: dict | None = None

    def __init__(
        self,
        url,
        max_idle_time_ms=60000,
        max_pool_size=50,
        models: List | None = None,
    ) -> None:
        self.url = url
        self.max_idle_time_ms = max_idle_time_ms
        self.max_pool_size = max_pool_size
        self.models = models if models else []

    async def get_session(self) -> pymongo.MongoClient:
        return self.client[self.parameters["database"]]

    async def connect(self):
        self.parameters = pymongo.uri_parser.parse_uri(self.url)
        self.client = AsyncIOMotorClient(
            self.url,
            maxIdleTimeMS=self.max_idle_time_ms,
            maxPoolSize=self.max_pool_size,
        )

    async def disconnect(self):
        self.client.close()
