from typing import Any, Annotated
from fastapi import APIRouter, Depends
from core.config import authorization, ecp_client
from schemas import PromotionalSMSStatus
from swagger import promotional_sms

from services import PromotionalService
from core.config import get_promotional_service


router = APIRouter()


@router.post(
    "/promotional_sms", responses=promotional_sms.promotional_sms_sample_response
)
async def promotional_sms(
    promotional_sms: PromotionalSMSStatus,
    promotional_service: Annotated[
        PromotionalService, Depends(get_promotional_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Change subscriber promotional sms status
    - **status**: Defines promotional sms status

    Raises:

        401: If JWT token is invalid

    Returns:

        message: Represents operation status

    """
    promotional_sms = promotional_sms.model_dump()
    await promotional_service.dnd_add_remove(
        profile["phone_number"], promotional_sms["status"]
    )
    return {"message": "done"}
