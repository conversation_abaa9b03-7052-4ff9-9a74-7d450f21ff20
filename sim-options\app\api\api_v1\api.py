from api.api_v1.endpoints import (
    auto_topup,
    dordaneh,
    internet_usage_control,
    notification_preferences,
    packages,
    pre2post,
    promotional_sms,
    puk,
    registered_numbers,
    registered_numbers_qc_uat,
    rewards,
    roaming,
    sim_upgrade,
    summary,
    suspension,
    suspension_reason,
    tariff_plan,
    transactions_history,
    vouchers,
    favorite_numbers,
    termination_report,
    registration_file,
)
from core.config import settings


from fastapi import APIRouter

api_router = APIRouter()
api_router.include_router(summary.router, prefix="", tags=["summary"])
api_router.include_router(auto_topup.router, prefix="", tags=["auto_topup"])
api_router.include_router(rewards.router, prefix="", tags=["rewards"])
api_router.include_router(roaming.router, prefix="", tags=["roaming"])
api_router.include_router(
    internet_usage_control.router, prefix="", tags=["internet_usage_control"]
)
api_router.include_router(promotional_sms.router, prefix="", tags=["promotional_sms"])
api_router.include_router(
    transactions_history.router, prefix="", tags=["transactions_history"]
)

api_router.include_router(
    notification_preferences.router,
    prefix="/notification_preferences",
    tags=["notification_preferences"],
)
api_router.include_router(tariff_plan.router, prefix="", tags=["tariff_plan"])
api_router.include_router(vouchers.router, prefix="", tags=["vouchers"])
api_router.include_router(packages.router, prefix="", tags=["packages"])
api_router.include_router(suspension.router, prefix="", tags=["suspension"])
api_router.include_router(puk.router, prefix="", tags=["puk"])

if settings.ENVIRONMENT in ["uat", "qc"]:
    api_router.include_router(
        registered_numbers_qc_uat.router,
        prefix="/registered_numbers",
        tags=["registered_numbers_ng"],
    )
else:
    api_router.include_router(
        registered_numbers.router,
        prefix="/registered_numbers",
        tags=["registered_numbers_ng"],
    )
api_router.include_router(pre2post.router, prefix="", tags=["pre2post"])
api_router.include_router(dordaneh.router, prefix="", tags=["dordaneh"])
api_router.include_router(
    sim_upgrade.router, prefix="/sim_upgrade", tags=["sim_upgrade"]
)
api_router.include_router(
    suspension_reason.router, prefix="/activation", tags=["activation"]
)
api_router.include_router(favorite_numbers.router, prefix="", tags=["favorite_offers"])

api_router.include_router(
    termination_report.router, prefix="/termination_report", tags=["reporting"]
)
# api_router.include_router(
#     registration_file.router, prefix="", tags=["registration_file"]
# )
