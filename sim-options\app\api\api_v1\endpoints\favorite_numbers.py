from typing import Any, Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi.responses import JSONResponse, Response

from core.config import authorization, reco_client

from schemas import FavoriteNumberInput, DeleteFavoriteNumberInput

from swagger import reco

from services import FavoriteNumberService
from core.config import get_favorite_numbers_service
from services.favorite_numbers_service import (
    RemoveResult,
    AddBNumberResult,
    UpdateBNumberResult,
)

router = APIRouter()


@router.get("/favorite_number", responses=reco.get_favorite_number_sample_response)
async def list_favorite_numbers(
    fav_number_service: Annotated[
        FavoriteNumberService, Depends(get_favorite_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    response = await fav_number_service.get_list(
        profile["phone_number"], profile["customer_type"], profile["language"]
    )
    return response


@router.post("/favorite_number", responses=reco.post_favorite_number_sample_response)
async def add_favorite_number(
    fn_input: FavoriteNumberInput,
    fav_number_service: Annotated[
        FavoriteNumberService, Depends(get_favorite_numbers_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    fn_input = fn_input.model_dump()
    res: AddBNumberResult = await fav_number_service.add_numbers(
        profile["phone_number"],
        profile["customer_type"],
        profile["language"],
        fn_input["b_number"],
        fn_input["nick_name"],
    )

    if res.error_status:
        if res.status_message == "invalid number":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/adding/failed/invalid_number",
                    "title": "Adding B-number failed",
                    "status": "400",
                    "message": "Invalid number",
                },
            )
        elif res.status_message == "invalid nickname":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/adding/failed/invalid_nickname",
                    "title": "Adding B-number failed",
                    "status": "400",
                    "message": "Invalid nickname",
                },
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/adding/failed/unknown",
                    "title": "Adding B-number failed",
                    "status": "400",
                    "message": "You can't add this number to your favorite numbers",
                },
            )

    return Response(status_code=200)


@router.delete(
    "/favorite_number", responses=reco.delete_favorite_number_sample_response
)
async def delete_favorite_number(
    fn_input: DeleteFavoriteNumberInput,
    fav_number_service: FavoriteNumberService = Depends(get_favorite_numbers_service),
    profile: dict = Depends(authorization),
) -> Any:

    fn_input = fn_input.model_dump()

    result: RemoveResult = await fav_number_service.remove_numbers(
        profile["phone_number"],
        profile["customer_type"],
        profile["language"],
        fn_input["b_numbers"],
    )

    if not result.success:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/favorite_numbers/removing/failed",
                "title": "Removing B-number failed",
                "status": "400",
                "message": f"Removing favorite numbers faild",
            },
        )

    return result


@router.put("/favorite_number", responses=reco.put_favorite_number_sample_response)
async def update_favorite_number(
    fn_input: FavoriteNumberInput,
    fav_number_service: FavoriteNumberService = Depends(get_favorite_numbers_service),
    profile: dict = Depends(authorization),
) -> Any:

    fn_input = fn_input.model_dump()
    res: UpdateBNumberResult = await fav_number_service.update_numbers(
        profile["phone_number"],
        profile["customer_type"],
        profile["language"],
        fn_input["b_number"],
        fn_input["nick_name"],
    )

    if res.error_status:
        if res.status_message == "invalid number":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/updating/failed/invalid_number",
                    "title": "Updating B-number failed",
                    "status": "400",
                    "message": "Invalid number",
                },
            )
        elif res.status_message == "invalid nickname":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/updating/failed/invalid_nickname",
                    "title": "Updating B-number failed",
                    "status": "400",
                    "message": "Invalid nickname",
                },
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/favorite_numbers/updating/failed/unknown",
                    "title": "Updating B-number failed",
                    "status": "400",
                    "message": "You can't add this number to your favorite numbers",
                },
            )

    return Response(status_code=200)
