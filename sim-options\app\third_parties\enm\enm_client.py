import json
import xmltodict
import base64

from ngmi_http import HTTPBase
from .parser import XML

class ENMClient(HTTPBase):
    def __init__(
        self, base_url, enm_username, enm_password, logger, http_client=None
    ) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.token = base64.b64encode(f"{enm_username}:{enm_password}".encode("utf-8"))
        self.xml_proccesor = XML()
        self.logger = logger

    async def _call_api(
        self,
        url,
        payload,
        headers,
        api_name,
        subject,
        request_parser=None
    ):
        # if not request_parser:
        #     request_parser = self.xml_proccesor.enm_request_parser

        await self.logger.info("headers", str(headers),)
            
        response = await self._call_soap_api(
            url=url,
            payload=payload,
            headers=headers,
            api_name=api_name,
            subject=subject,
            service="ENM",
            # request_parser=request_parser
        )

        return json.loads(json.dumps(xmltodict.parse(response["data"])))

    async def get_notif_preferences(self, phone_number):

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "Authorization": f"Basic {self.token.decode()}",
        }
        body = f"""<soapenv:Envelope
            xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
            <soapenv:Body>
                    <ns1:GetSubscriberNotificationPreferencesRequest
                            xmlns:ns1="http://com.ericsson/enk/ne/schema/1_0/notificationPreferenceTypes">
                            <ns1:msisdn>{phone_number}</ns1:msisdn>
                    </ns1:GetSubscriberNotificationPreferencesRequest>
            </soapenv:Body>
            </soapenv:Envelope>"""

        url = f"{self.base_url}/NeWs/SubscriberNotificationPreferences"

        response = await self._call_api(
            url=url,
            payload=body,
            headers=headers,
            api_name="GetSubscriberNotificationPreferencesRequest",
            subject=phone_number,
        )
        data = response["soap:Envelope"]["soap:Body"][
            "GetSubscriberNotificationPreferencesResponse"
        ]["subscriberNotificationPreferences"]

        return data

        res = {
            "language": data["language"].lower(),
            "sms_phone_number": (
                data["preferredMsisdn"]
                if "nil" not in str(data["preferredMsisdn"]).lower()
                else ""
            ),
            "email_address": (
                data["emailAddress"].lower()
                if "nil" not in str(data["emailAddress"]).lower()
                else ""
            ),
            "email_notification": "EMAIL" in data["channel"],
            "push_notification": "APP" in data["channel"],
            "sms_notification": "SMS" in data["channel"],
        }
        return res

    async def set_notif_preferences(
        self,
        phone_number,
        language,
        sms_phone_number,
        email_address,
        push_notification,
        email_notification,
        sms_notification,
    ):

        selected_channels = []
        if push_notification:
            selected_channels.append("APP")
        if email_notification:
            selected_channels.append("EMAIL")
        if sms_notification:
            selected_channels.append("SMS")

        channels = "\n".join(
            [f"<ns1:Channel>{channel}</ns1:Channel>" for channel in selected_channels]
        )

        headers = {
            "Content-Type": "text/xml;charset=UTF-8",
            "Authorization": f"Basic {self.token.decode()}",
        }

        body = f"""<?xml version="1.0" encoding="UTF-8"?>
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
                    <soapenv:Body>
                        <ns1:SetSubscriberNotificationPreferencesRequest xmlns:ns1="http://com.ericsson/enk/ne/schema/1_0/notificationPreferenceTypes">
                            <ns1:msisdn>{phone_number}</ns1:msisdn>
                            <ns1:language>
                                <ns1:Language>{language}</ns1:Language>
                            </ns1:language>
                            <ns1:preferredMsisdn>
                                <ns1:PreferredMsisdn>{sms_phone_number}</ns1:PreferredMsisdn>
                            </ns1:preferredMsisdn>
                            <ns1:emailAddress>
                                <ns1:EmailAddress>{email_address}</ns1:EmailAddress>
                            </ns1:emailAddress>
                            <ns1:channel>
                                {channels}
                            </ns1:channel>
                        </ns1:SetSubscriberNotificationPreferencesRequest>
                    </soapenv:Body>
                </soapenv:Envelope>"""

        url = f"{self.base_url}/NeWs/SubscriberNotificationPreferences"

        response = await self._call_api(
            url=url,
            payload=body,
            headers=headers,
            subject=phone_number,
            api_name="SetSubscriberNotificationPreferencesRequest",
        )

        response_code = response["soap:Envelope"]["soap:Body"][
            "SetSubscriberNotificationPreferencesResponse"
        ]["responseCode"]

        if response_code == "0":
            return True
        return False
