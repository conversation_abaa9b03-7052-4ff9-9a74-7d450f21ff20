import json
import xmltodict

from ngmi_http import HTTPBase
from .parser import XML


class DMSClient(HTTPBase):
    def __init__(self, end_point, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.end_point = end_point
        self.xml_processor = XML()

    async def _call_api(
        self, url, headers, payload, subject, api_name, timeout, request_parser=None
    ):
        if not request_parser:
            request_parser = self.xml_processor.dms_request_parser
        response = await self._call_soap_api(
            url=url,
            payload=payload,
            headers=headers,
            timeout=timeout,
            api_name=api_name,
            service="DMS",
            subject=subject,
            request_parser=request_parser,
        )

        return (
            json.loads(json.dumps(xmltodict.parse(response["data"]))),
            response["data"],
        )

    async def get_multi_accounts(self, phone_number):

        headers = {
            "Content-Type": "text/xml",
            "Authorization": "Basic TXlJcmFuY2VsbEFwcDoyMDIzTXkxcmFuM2UxMQ==",
        }

        payload_phone = f"0{phone_number[2:]}"

        payload = f'<?xml version="1.0" encoding="UTF-8"?>\r\n<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.dms.parstasmim.com/">\r\n    <soapenv:Header/>\r\n    <soapenv:Body>\r\n        <ws:getBase64Documents4MyIrancell>\r\n            <request>\r\n                <thirdpartySystem>\r\n                    <thirdpartyName>MyIrancell</thirdpartyName>\r\n                    <thirdpartySysName>MyIrancellApp</thirdpartySysName>\r\n                    <thirdpartyLoggedInUser>MyIrancellAdmin</thirdpartyLoggedInUser>\r\n                </thirdpartySystem>\r\n                <transactionId>1123456</transactionId>\r\n                <msisdn>{payload_phone}</msisdn>\r\n                <operation>MyIrancell</operation>\r\n            </request>\r\n        </ws:getBase64Documents4MyIrancell>\r\n    </soapenv:Body>\r\n</soapenv:Envelope>'

        jresp, _ = await self._call_api(
            url=self.end_point,
            headers=headers,
            payload=payload,
            subject=phone_number,
            api_name="dmsws_ws4myirancell",
            timeout=20,
        )
        return jresp
