otp_request_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Operation Failed",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/login/otp_request/too_many",
                    "title": "Too many OTP attempts in time window",
                    "detail": "You can only request for OTP once in 120 seconds. You can try in 107 seconds",
                    "params": {"phone_number": "************", "barred_for": "107"},
                }
            }
        },
    },
}


inquiry_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "OTP is invalid",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                    "title": "OTP is invalid",
                }
            }
        },
    },
    200: {
        "description": "Registered MSISDNs",
        "content": {
            "application/json": {
                "example": [
                    {
                        "phone_number": "989352003679",
                        "status": "Active",
                        "type": "Voice",
                        "termination_status": "not_requested",
                    },
                    {
                        "phone_number": "989026944816",
                        "status": "Active",
                        "type": "Voice",
                        "termination_status": "not_requested",
                    },
                    {
                        "phone_number": "989356990670",
                        "status": "Suspended",
                        "type": "Voice",
                        "termination_status": "not_requested",
                    },
                    {
                        "phone_number": "98**********",
                        "status": "Active",
                        "type": "TDD",
                        "termination_status": "not_requested",
                    },
                ]
            }
        },
    },
}
terminate_check_sample_responses = {
    400: {
        "description": "phone number can not be terminated",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                        "title": "Unable to retrive phone number informations.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
                        "title": "Termination failed due to profile type.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
                        "title": "SIM card termination failed due to too many requests.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
                        "title": "SIM card termination failed due to activation.",
                        "params": {"reasons": "reason"},
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
                        "title": "SIM card termination failed due to an active shared account.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                        "title": "SIM card termination failed due to debt.",
                        "params": {
                            "debt_type": "advance air time",
                            "amount": 100,
                        },
                    },
                ]
            }
        },
    },
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {},
}

termination_otp_request_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Operation Failed",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/termination/otp_request/too_many",
                        "title": "Too many OTP attempts in time window",
                        "detail": "You can only request for OTP once in 120 seconds. You can try in 107 seconds",
                        "params": {
                            "phone_number": "************",
                            "barred_for": "107",
                        },
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                        "title": "Unable to retrive phone number informations.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
                        "title": "Online termination operation failed due nid mismatch",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
                        "title": "Online termination operation failed due identity mismatch",
                    },
                ]
            }
        },
    },
}
terminate_request_sample_response = {
    400: {
        "description": "phone number can not be terminated",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                        "title": "OTP is invalid",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
                        "title": "SIM card termination failed due to profile type.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
                        "title": "SIM card termination failed due to too many requests on the same day.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
                        "title": "SIM card termination failed due to activation.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
                        "title": "SIM card termination failed due to an active shared account.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                        "title": "SIM card termination failed due to outstanding debt.",
                        "params": {"debt_type": "bill"},
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                        "title": "SIM card termination failed due to outstanding debt.",
                        "params": {"debt_type": "advance_air_time"},
                    },
                ]
            }
        },
    },
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Termination request submitted successfully",
        "content": {"application/json": {"example": {}}},
    },
}
terminate_cancel_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Termination request deleted successfully",
        "content": {"application/json": {"example": {}}},
    },
}
