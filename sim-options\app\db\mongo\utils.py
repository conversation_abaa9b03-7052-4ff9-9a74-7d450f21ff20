def termination_request_data(termination_request) -> dict:
    return {
        "id": str(termination_request["_id"]),
        "msisdn": termination_request["msisdn"],
        "request_datetime": termination_request["request_datetime"],
        "national_id": termination_request["national_id"],
        "first_name": termination_request["first_name"],
        "last_name": termination_request["last_name"],
        "father_name": termination_request["father_name"],
        "reason": termination_request["reason"],
        "status": termination_request["status"]
    }


def dordaneh_data(dordaneh) -> dict:
    return {
        "id": str(dordaneh["_id"]),
        "msisdn": dordaneh["msisdn"],
        "registration_date": dordaneh["registration_date"],
        "dordaneh": dordaneh["dordaneh"],
    }


def sim_upgrade_data(sim_upgrade_info) -> dict:
    return {
        "id": str(sim_upgrade_info["_id"]),
        "msisdn": sim_upgrade_info["msisdn"],
        "date": sim_upgrade_info["date"],
        "type": sim_upgrade_info["type"],
        "postal_info": sim_upgrade_info["postal_info"],
    }
