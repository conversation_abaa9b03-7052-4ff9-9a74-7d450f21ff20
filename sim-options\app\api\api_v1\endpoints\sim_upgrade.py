from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends, <PERSON><PERSON>, HTTPException
from core.config import eia_client
from core.config import authorization as jwt_authorization
from schemas import PostalValidationInput
from swagger import sim_upgrade

from ngmi_logging.utils import add_params_into_access_log

from services import SimUpgradeService
from core.config import get_sim_upgrade_service

router = APIRouter()


@router.get("", responses=sim_upgrade.sim_upgrade_sample_responses)
async def get_sim_upgrade_eligibility(
    sim_upgrade_service: Annotated[SimUpgradeService, Depends(get_sim_upgrade_service)],
    profile: Annotated[dict, Depends(jwt_authorization)],
) -> Any:
    """get sim upgrade to 4G eligibility

    Raises:

        401: If JWT token is invalid

    Returns:

        is_eligible_4g: Represents if sim is eligible

    """
    res = await sim_upgrade_service.sim_upgrade_eligibility(
        phone_number=profile["phone_number"],
        fields=["sim_category"],
        customer_type=profile["customer_type"],
    )

    add_params_into_access_log(key="target", value="fourthgnav")
    add_params_into_access_log(key="source", value="services")

    return res


@router.post("/postal/validate", responses=sim_upgrade.postal_validate_sample_responses)
async def postal_validate(
    postal_validate_input: PostalValidationInput,
    sim_upgrade_service: Annotated[SimUpgradeService, Depends(get_sim_upgrade_service)],
    authorization: str = Header(None),
    accept_language: str = Header("fa"),
) -> Any:
    """Request for validation of postal code
    Raises:

        400: Invalid postal

    Returns:

        None

    """
    postal_validate_input = postal_validate_input.model_dump()

    if authorization:
        profile = await jwt_authorization(
            authorization,
            accept_language,
        )
        phone_number = profile["phone_number"]

    else:
        if not postal_validate_input["phone_number"]:
            raise HTTPException(status_code=422, detail="phone_number is mandatory.")
        else:
            phone_number = postal_validate_input["phone_number"]

    postal_code = postal_validate_input["postal_code"]

    res = await sim_upgrade_service.sim_upgrade_validate_postal_code(
        phone_number=phone_number, postal_code=postal_code
    )

    if res["error_status"]:
        return {"is_valid": False}
    else:
        return {"is_valid": True} | res["data"]
