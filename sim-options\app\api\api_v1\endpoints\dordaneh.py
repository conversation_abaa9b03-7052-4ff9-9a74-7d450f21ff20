from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse, Response

from typing import Any, Annotated

from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient

from core.config import authorization, notification_client, get_dordane_service
from core.settings import settings
from db.mongo.database import get_database
from schemas import DordanehInput, DordanehRequestInput, DordaneResponse
from swagger import dordaneh
from services import DordaneService
from services.dordane_service import (
    DordaneList,
    AddDordane,
    DordaneDetails,
    RemoveDordane,
)


router = APIRouter()


@router.get(
    "/dordaneh",
    status_code=status.HTTP_200_OK,
    # response_model=DordaneResponse,
    # responses=dordaneh.get_dordaneh_sample_response,
)
async def list_active_dordaneh(
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    dordane_service: Annotated[DordaneService, Depends(get_dordane_service)],
):
# ) -> DordaneResponse:

    result: DordaneList = await dordane_service.active_dordane_list(
        session=session,
        phone_number=profile["phone_number"],
        registration_date=profile.get("mtni", {}).get("registration_date", ""),
    )

    return result.dordane_numbers


@router.post("/dordaneh", responses=dordaneh.post_dordaneh_sample_response)
async def add_dordaneh(
    dordaneh_input: DordanehRequestInput,
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    dordane_service: Annotated[DordaneService, Depends(get_dordane_service)],
) -> Any:

    dordaneh_input = dordaneh_input.model_dump()
    otp, dordaneh_msisdn = dordaneh_input["otp"], dordaneh_input["phone_number"]

    dordaneh_customer_profile: DordaneDetails = (
        await dordane_service.check_dordane_details(
            dordaneh_msisdn, fields=["sim_type", "mtni", "customer_type"]
        )
    )
    if not dordaneh_customer_profile.mtni:

        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/authentication/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add your number as a Dordaneh. Please seek support from the online chat",
            },
        )
    if dordaneh_customer_profile.sim_type == "td":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/authentication/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add your number as a Dordaneh. Please seek support from the online chat",
            },
        )

    res = await notification_client.validate_otp(
        recipient=dordaneh_msisdn,
        otp=otp,
        intention="dordaneh_activation",
        originated_msisdn=profile["phone_number"],
    )
    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    result: AddDordane = await dordane_service.add_dordane(
        session=session,
        owner_msisdn=profile["phone_number"],
        dordane_msisdn=dordaneh_msisdn,
        registration_date=profile.get("mtni", {}).get("registration_date", ""),
        dordaneh_customer_type=dordaneh_customer_profile.customer_type,
    )
    if not result.dordane_eligibility:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/activation/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add your number as a Dordaneh. Please seek support from the online chat",
            },
        )
    if result.dordane_existence:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/activation/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add this phone number as a Dordaneh. Please seek support from the online chat",
            },
        )
    if result.eia_queued:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/activation/failed_too_many",
                "title": "Dordaneh activation failed due to too many requests",
                "status": "400",
                "message": "You can't add this number as a Dordaneh. Please try again after 24 hours",
            },
        )
    if result.eia_error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/activation/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add this number as a Dordaneh. Please seek support from the online chat",
            },
        )
    if not result.success:
        return JSONResponse(status_code=400, content="error")
    else:
        return JSONResponse(status_code=200, content="success")


@router.delete("/dordaneh", responses=dordaneh.delete_dordaneh_sample_response)
async def remove_dordaneh(
    dordaneh_input: DordanehInput,
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    dordane_service: Annotated[DordaneService, Depends(get_dordane_service)],
) -> Any:

    dordaneh_input = dordaneh_input.model_dump()

    result: RemoveDordane = await dordane_service.remove_dordane(
        session=session,
        owner_msisdn=profile["phone_number"],
        dordane_msisdn=dordaneh_input["phone_number"],
        registration_date=profile.get("mtni", {}).get("registration_date", ""),
    )

    if not result.dordane_existence:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/deactivation/failed",
                "title": "Dordaneh deactivation failed",
                "status": "400",
                "message": "You can't remove this number from your Dordanehs. Please seek support from the online chat",
            },
        )
    if result.eia_queued:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/deactivation/failed_too_many",
                "title": "Dordaneh deactivation failed due to too many requests",
                "status": "400",
                "message": "You can't remove this number from your Dordanehs. Please try again after 24 hours",
            },
        )
    if result.eia_error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/deactivation/failed",
                "title": "Dordaneh deactivation failed",
                "status": "400",
                "message": "You can't remove this number from your Dordanehs. Please seek support from the online chat",
            },
        )

    data = {
        "dordaneh_msisdn": dordaneh_input["phone_number"],
    }

    accept_language = profile.get("language", "fa")
    await notification_client.send_sms_notification(
        template_id=settings.SMS_TEMPALTE_DORDANEH_REMOVE,
        template_data=data,
        recipient=dordaneh_input["phone_number"],
        off_net=False,
        accept_language=accept_language,
    )


@router.post("/dordaneh/otp/request")
async def send_otp_to_dordaneh(
    dordaneh_otp: DordanehInput,
    dordane_service: Annotated[DordaneService, Depends(get_dordane_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    dordaneh_otp = dordaneh_otp.model_dump()

    dordaneh_customer_profile: DordaneDetails = (
        await dordane_service.check_dordane_details(
            dordaneh_otp["phone_number"],
            fields=["sim_type", "notification_phone_number", "mtni"],
        )
    )

    if not dordaneh_customer_profile.mtni:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/dordaneh/authentication/failed",
                "title": "Dordaneh activation failed",
                "status": "400",
                "message": "You can't add your number as a Dordaneh. Please seek support from the online chat",
            },
        )

    res = await notification_client.send_sms_otp(
        phone_number=profile["phone_number"],
        recipient=dordaneh_otp["phone_number"],
        off_net=False,
        language=profile["language"],
        intention="dordaneh_activation",
        client_id=profile["client_id"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))

    if dordaneh_customer_profile.sim_type == "td":
        return {"send_to": dordaneh_customer_profile.notification_phone_number}
    return {"send_to": dordaneh_otp["phone_number"]}
