import json
import xmltodict
from bs4 import BeautifulSoup
from ngmi_eia import EIAClient

from ngmi_http import HTT<PERSON><PERSON><PERSON>
from .parser import XML

from pydantic import BaseModel
from typing import Optional


class BNumbersResult(BaseModel):
    status_code: int
    b_numbers: Optional[list] = None

class AddBNumbersResult(BaseModel):
    status_code: int

class RemoveBNumbersResult(BaseModel):
    status_code: int

class UpdateBNumbersResult(BaseModel):
    status_code: int

class RecoClient(HTTPBase):
    def __init__(
        self,
        endpoint,
        bnumber_username,
        bnumber_password,
        eia_client: EIAClient,
        logger,
        http_client=None,
    ) -> None:
        super().__init__(logger, http_client)
        self.endpoint = endpoint
        self.bnumber_username = bnumber_username
        self.bnumber_password = bnumber_password
        self.eia_client = eia_client
        self.xml_proccesor = XML()

    async def _call_api(
        self,
        endpoint,
        payload,
        api_name,
        subject="",
        headers=None,
        subservice_name="",
        timeout=None,
        request_parser=None
    ):
        if not request_parser:
            request_parser = self.xml_proccesor.reco_request_parser

        response = await self._call_soap_api(
            url=endpoint,
            headers=headers,
            payload=payload,
            api_name=api_name,
            subservice_name=subservice_name,
            service="RECO",
            subject=subject,
            timeout=timeout,
            request_parser=request_parser
        )
        return response["data"]

    async def get_b_numbers(self, phone_number, customer_type, language):
        username = self.bnumber_password
        password = self.bnumber_username
        customer_type = customer_type.capitalize()
        lang = "1" if language == "fa" else "0"

        headers = headers = {"Content-Type": "application/xml"}
        payload = f"<Request>\r\n    <requestType>VIEWBNUMBERLIST</requestType>\r\n    <senderSystem>IRANCell</senderSystem>\r\n    <channel>IRANCell</channel>\r\n    <userName>{username}</userName>\r\n    <pwd>{password}</pwd>\r\n    <MSISDN>{phone_number}</MSISDN>\r\n    <languageID>{lang}</languageID>\r\n    <serviceClass>{customer_type}</serviceClass>\r\n</Request>"

        response = await self._call_api(
            endpoint=self.endpoint,
            payload=payload,
            api_name="VIEWBNUMBERLIST",
            subject=phone_number,
            headers=headers,
        )

        res = []
        xml_resp = BeautifulSoup(response, "lxml").prettify()
        jresp = json.loads(json.dumps(xmltodict.parse(xml_resp)))
        jresp = jresp["html"]["body"]["response"]

        status_code = int(jresp["statuscode"])
        b_numbers = jresp.get("blist", {}).get("bnumber", [])
        if  type(b_numbers) is not list:
            b_numbers = [b_numbers]
        
        return BNumbersResult(status_code= status_code, b_numbers= b_numbers)
    
        # status_code = int(jresp["statuscode"])
        # if status_code != 0:
        #     return res

        # b_numbers = jresp["blist"]["bnumber"]
        # if  type(b_numbers) is not list:
        #     b_numbers = [b_numbers]

        # for b_number in b_numbers:
        #     res.append(
        #         {
        #             "favorite_number": b_number["msisdn"],
        #             "nick_name": b_number["nickname"],
        #             "sim_type": b_number["bpartytype"],
        #         }
        #     )

        # return res

    # async def add_b_number(
    #     self, phone_number, customer_type, language, b_number, nick_name
    # ):
    async def add_b_number(
        self, phone_number, customer_type, b_number, nick_name, b_number_sim_type, b_number_customer_type, language
    ):

        username = self.bnumber_username
        password = self.bnumber_password

        res = {"error_status": False, "status_message": ""}

        # customer_profile = await self.eia_client.get_customer_profile(b_number)
        # if not customer_profile["mtni"]:
        #     res["error_status"] = True
        #     res["status_message"] = "invalid number"
        #     return res

        # b_number_customer_type = customer_profile["customer_type"].capitalize()
        # b_number_sim_type = "GSM" if customer_profile["sim_type"] == "fd" else "TDLTE"
        # customer_type = customer_type.capitalize()
        # lang = "1" if language == "fa" else "0"

        b_number_customer_type = b_number_customer_type.capitalize()
        b_number_sim_type = "GSM" if b_number_sim_type == "fd" else "TDLTE"
        customer_type = customer_type.capitalize()
        lang = "1" if language == "fa" else "0"

        headers = headers = {"Content-Type": "application/xml"}
        payload = f"<Request>\r\n    <requestType>ADD_B_NUMBER</requestType>\r\n    <senderSystem>IRANCell</senderSystem>\r\n    <channel>IRANCell</channel>\r\n    <userName>{username}</userName>\r\n    <pwd>{password}</pwd>\r\n    <MSISDN>{phone_number}</MSISDN>\r\n    <languageID>{lang}</languageID>\r\n    <serviceClass>{customer_type}</serviceClass>\r\n    <paramList>\r\n        <param>\r\n            <name>bPartyMSISDN</name>\r\n            <value>{b_number}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartytype</name>\r\n            <value>{b_number_sim_type}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartyServiceClass</name>\r\n            <value>{b_number_customer_type}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartyLanguageID</name>\r\n            <value>{lang}</value>\r\n        </param>\r\n        <param>\r\n            <name>serviceType</name>\r\n            <value>giftBolton</value>\r\n        </param>\r\n        <param>\r\n            <name>nickName</name>\r\n            <value>{nick_name}</value>\r\n        </param>\r\n    </paramList>\r\n</Request>"

        response = await self._call_api(
            endpoint=self.endpoint,
            headers=headers,
            payload=payload,
            api_name="ADD_B_NUMBER",
            subject=phone_number,
        )

        xml_resp = BeautifulSoup(response, "xml").prettify()
        jresp = json.loads(json.dumps(xmltodict.parse(xml_resp)))
        status_code = int(jresp["Response"]["statusCode"])
        return AddBNumbersResult(status_code= status_code)

        if status_code != 0:
            res["error_status"] = True
            if status_code == 1058:
                res["status_message"] = "invalid nickname"

        return res

    async def delete_b_number(
        self, phone_number, customer_type, language, b_number_msisdn, b_number_sim_type
    ):

        username = self.bnumber_username
        password = self.bnumber_password
        res = {"error_status": False}

        customer_type = customer_type.capitalize()
        lang = 1 if language == "fa" else 0

        headers = headers = {"Content-Type": "application/xml"}
        payload = f"<Request>\r\n    <requestType>DELETEBNUMBER</requestType>\r\n    <senderSystem>IRANCell</senderSystem>\r\n    <channel>IRANCell</channel>\r\n    <transDateTime>12092022061117</transDateTime>\r\n    <transactionId>C1C2784393D9B5</transactionId>\r\n    <userName>{username}</userName>\r\n    <pwd>{password}</pwd>\r\n    <MSISDN>{phone_number}</MSISDN>\r\n    <languageID>{lang}</languageID>\r\n    <serviceClass>{customer_type}</serviceClass>\r\n    <paramList>\r\n        <param>\r\n            <name>bPartyMSISDN</name>\r\n            <value>{b_number_msisdn}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartytype</name>\r\n            <value>{b_number_sim_type}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartyLanguageID</name>\r\n            <value>{lang}</value>\r\n        </param>\r\n        <param>\r\n            <name>serviceType</name>\r\n            <value>TopUp</value>\r\n        </param>\r\n    </paramList>\r\n</Request>"

        response = await self._call_api(
            endpoint=self.endpoint,
            payload=payload,
            subject=phone_number,
            headers=headers,
            api_name="DELETEBNUMBER",
        )

        xml_resp = BeautifulSoup(response, "xml").prettify()
        jresp = json.loads(json.dumps(xmltodict.parse(xml_resp)))
        status_code = int(jresp["Response"]["statusCode"])

        return RemoveBNumbersResult(status_code= status_code)

        if status_code != 0:
            res["error_status"] = True

        return res

    async def update_b_number(
        self, phone_number, customer_type, language, b_number, nick_name, b_number_customer_type, b_number_sim_type
    ):

        username = self.bnumber_username
        password = self.bnumber_password

        res = {"error_status": False, "status_message": ""}

        # customer_profile = await self.eia_client.get_customer_profile(b_number)

        # if not customer_profile["mtni"]:
        #     res["error_status"] = True
        #     res["status_message"] = "invalid number"
        #     return res

        # b_number_customer_type = customer_profile["customer_type"].capitalize()
        # b_number_sim_type = "GSM" if customer_profile["sim_type"] == "fd" else "TDLTE"
        # lang = "1" if language == "fa" else "0"
        # customer_type = customer_type.capitalize()

        b_number_customer_type = b_number_customer_type.capitalize()
        b_number_sim_type = "GSM" if b_number_sim_type == "fd" else "TDLTE"
        customer_type = customer_type.capitalize()
        lang = "1" if language == "fa" else "0"

        headers = headers = {"Content-Type": "application/xml"}
        payload = f"<Request>\r\n    <requestType>SETNICKNAME</requestType>\r\n    <senderSystem>IRANCell</senderSystem>\r\n    <channel>IRANCell</channel>\r\n    <transDateTime>22092022131025</transDateTime>\r\n    <transactionId>C336FFC3251F7E</transactionId>\r\n    <userName>{username}</userName>\r\n    <pwd>{password}</pwd>\r\n    <MSISDN>{phone_number}</MSISDN>\r\n    <languageID>{lang}</languageID>\r\n    <serviceClass>{customer_type}</serviceClass>\r\n    <paramList>\r\n        <param>\r\n            <name>bPartyMSISDN</name>\r\n            <value>{b_number}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartytype</name>\r\n            <value>{b_number_sim_type}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartyServiceClass</name>\r\n            <value>{b_number_customer_type}</value>\r\n        </param>\r\n        <param>\r\n            <name>bPartyLanguageID</name>\r\n            <value>0</value>\r\n        </param>\r\n        <param>\r\n            <name>serviceType</name>\r\n            <value>giftbolton</value>\r\n        </param>\r\n        <param>\r\n            <name>nickName</name>\r\n            <value>{nick_name}</value>\r\n        </param>\r\n    </paramList>\r\n</Request>"

        response = await self._call_api(
            endpoint=self.endpoint,
            payload=payload,
            subject=phone_number,
            headers=headers,
            api_name="UPDATE_B_NUMBER",
        )

        res = {"error_status": False}
        xml_resp = BeautifulSoup(response, "xml").prettify()
        jresp = json.loads(json.dumps(xmltodict.parse(xml_resp)))
        status_code = int(jresp["Response"]["statusCode"])
        return UpdateBNumbersResult(status_code= status_code)

        if status_code != 0:
            res["error_status"] = True
            if status_code == 1058:
                res["status_message"] = "invalid nickname"

        return res
