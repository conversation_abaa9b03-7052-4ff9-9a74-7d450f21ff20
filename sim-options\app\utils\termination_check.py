from datetime import datetime, timedelta
from core.config import settings, eia_client, hsdp_client, aat_client
from db.mongo.helpers import count_termination_requests, get_termination_requests
from utils import persian_text_normalizer


async def termination_check_number(phone_number, subscriber_phone_number, language, db):

    res = {
        "error_status": False,
        "termination_possibility": False,
        "reason": "",
        "data": {},
    }

    customer_profile = await eia_client.get_customer_profile(
        phone_number=subscriber_phone_number
    )
    sub_nid = customer_profile["national_id"]

    customer_profile = await eia_client.get_customer_profile(phone_number=phone_number)
    nid = customer_profile["national_id"]

    if sub_nid != nid:
        res["error_status"] = True
        res["reason"] = "failed_due_not_own"
        return res

    query = {
        "msisdn": phone_number,
        "status": "in-progress",
    }
    termination_requests = await get_termination_requests(query, db)
    if termination_requests:
        res["reason"] = "failed_due_duplication"
        return res

    now = datetime.now()
    last_day = now - timedelta(days=1)
    query = {
        "national_id": nid,
        "request_datetime": {"$gte": last_day, "$lte": now},
        "status": {"$in": ["cancelled", "in-progress"]},
    }
    today_requests = await count_termination_requests(query, db)
    if today_requests > int(settings.TERMINATION_DAILY_LIMIT):
        res["reason"] = "failed_due_requests"
        return res
    last_week = now - timedelta(days=7)
    query = {
        "national_id": nid,
        "request_datetime": {"$gte": last_week, "$lte": now},
        "status": {"$in": ["cancelled", "in-progress"]},
    }
    this_week_requests = await count_termination_requests(query, db)
    if this_week_requests > int(settings.TERMINATION_WEEKLY_LIMIT):
        res["reason"] = "failed_due_requests"
        return res
    if customer_profile["profile_type"] != "individual":
        res["reason"] = "failed_due_profile_type"
        return res

    if customer_profile["customer_type"] == "td":
        res["reason"] = "failed_due_sim_type"
        return res

    status = await eia_client.suspension_status(phone_number=phone_number)
    if status["error_status"]:
        res["error_status"] = True
        return res
    elif "data" in status:
        if not status["data"]["is_active"]:
            reasons = []
            reasons_res = await eia_client.suspension_reasons(phone_number=phone_number)
            if not reasons_res["error_status"]:
                reasons = reasons_res["data"]
            for reason in reasons:
                reason["description"] = reason["description"][language]
                reason["guidance"] = reason["guidance"][language]
            res["reason"] = "failed_due_activation"
            res["data"] = {"is_active": status["data"]["is_active"], "reasons": reasons}
            return res
    else:
        res["error_status"] = True
        return res

    shared_account_status = await eia_client.shared_account_get_status(
        phone_number=phone_number
    )
    if shared_account_status["error_status"]:
        res["error_status"] = True
        return res
    shared_account_status = shared_account_status["data"]["is_activated"]
    if shared_account_status:
        res["reason"] = "faild_due_sharedaccount"
        return res

    if customer_profile["customer_type"] == "postpaid":
        account_details = await eia_client.get_account_details(
            phone_number=phone_number, customer_type="postpaid", language=language
        )
        if (account_details["current_balance"] > 0) or (
            account_details["outstanding_balance"] > 0
        ):
            amount = (
                account_details["current_balance"]
                if account_details["current_balance"]
                else account_details["outstanding_balance"]
            )
            # bill = "صورت حساب" if language == "fa" else "bill"
            res["reason"] = "faild_due_debt"
            res["data"] = {"debt_type": "bill", "amount": amount}
            return res

    else:
        current_debt = 0
        fake_id_res = await hsdp_client.hsdp_get_fake_id(
            phone_number=phone_number,
        )
        if not fake_id_res["error_status"] and fake_id_res["data"].startswith("f-"):
            fake_id = fake_id_res["data"]
            aat_res = await aat_client.aat_qualification_check(
                fake_id=fake_id,
                phone_number=phone_number,
            )
            if aat_res["error_status"]:
                res["error_status"] = True
                return res

            if not aat_res["error_status"] and "loan_balance" in aat_res["data"]:
                current_debt = aat_res["data"]["loan_balance"]

        if current_debt > 0:
            # advance_air_time = (
            #     "خدمات اضطراری" if language == "fa" else "advance air time"
            # )
            res["reason"] = "faild_due_debt"
            res["data"] = {"debt_type": "advance_air_time", "amount": current_debt}
            return res
    res["termination_possibility"] = True
    return res


async def termination_check_entry(
    phone_number, national_id, first_name, last_name, father_name
):
    res = {
        "error_status": False,
        "termination_possibility": True,
        "reason": "",
    }

    customer_profile = await eia_client.get_customer_profile(phone_number=phone_number)
    if customer_profile["national_id"].lower() != national_id.lower():
        res["termination_possibility"] = False
        res["reason"] = "failed_due_nid_mismatch"
        return res

    first_name = persian_text_normalizer(first_name)
    last_name = persian_text_normalizer(last_name)
    father_name = persian_text_normalizer(father_name)
    enterd_first_name = persian_text_normalizer(customer_profile["first_name"])
    enterd_last_name = persian_text_normalizer(customer_profile["last_name"])
    enterd_father_name = persian_text_normalizer(customer_profile["father_name"])

    if (
        first_name != enterd_first_name
        or last_name != enterd_last_name
        or father_name != enterd_father_name
    ):
        res["termination_possibility"] = False
        res["reason"] = "failed_due_identity_mismatch"
        return res

    res["termination_possibility"] = True
    return res
