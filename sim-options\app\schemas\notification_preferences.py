from typing import Union

from pydantic import BaseModel, field_validator
from pydantic import EmailStr
from pydantic import model_validator


class EmailEmptyAllowedStr(EmailStr):
    @classmethod
    def field_validator(cls, value: str) -> str:
        if value == "":
            return value
        return super().validate(value)


class NotificationPreferencesOTP(BaseModel):
    phone_number: str | None = None
    email_address: str | None = None

    @model_validator(mode="before")
    def notifications_validator(self):
        if not self.get("phone_number") and not self.get("email_address"):
            raise ValueError("At least one notification recipient must be present")
        return self

    @field_validator("phone_number")
    def validate_phone_number(cls, value):
        if value is not None:
            value = value.replace(" ", "")  # Remove spaces if present
            if not value.isdigit():
                raise ValueError("Phone number must contain only digits")
            if len(value) not in (10, 12):
                raise ValueError("Phone number must have either 10 or 12 digits")
        return value


class NotificationPreferences(BaseModel):
    push_notification: Union[bool, None] = None
    sms_notification: Union[bool, None] = None
    email_notification: Union[bool, None] = None
    sms_phone_number: Union[str, None] = None
    sms_otp: Union[str, None] = None
    email_address: Union[EmailEmptyAllowedStr, None] = None
    email_otp: Union[str, None] = None

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "push_notification": True,
                    "sms_notification": True,
                    "email_notification": False,
                    "sms_phone_number": "989999999999",
                    "sms_otp": "1111",
                    "email_address": "<EMAIL>",
                    "email_otp": "1111",
                }
            ]
        }
    }

    @field_validator("sms_phone_number")
    def sms_phone_number_valid(cls, v):
        if v:
            assert len(v) == 12 and v.startswith("98"), "Phone number is invalid"
        return v

    @model_validator(mode="before")
    def notifications_validator(self):
        if not (
            self.get("push_notification") != None
            or self.get("sms_notification") != None
            or self.get("email_notification") != None
        ):
            raise ValueError("At least one notification must be present")

        if self.get("email_notification") and not self.get("email_otp"):
            raise ValueError("Email OTP is required.")

        return self
