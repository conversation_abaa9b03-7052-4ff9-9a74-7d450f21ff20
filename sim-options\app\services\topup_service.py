from datetime import datetime
from pydantic import BaseModel
from typing import Optional, List

from ngmi_http import InvalidUpstreamResponse


class AutoTopupService(BaseModel):
    recharge_type: Optional[str]
    threshold_id: Optional[str]
    amount: Optional[str]
    bank_id: Optional[str]
    bank_name: Optional[str]
    service_id: Optional[str]
    service_type: Optional[str]


class StatusResult(BaseModel):
    status: str
    services: List[AutoTopupService]


class DeactiveResult(BaseModel):
    result_code: int


class TopUpService:
    def __init__(
        self,
        auto_recharg_client,
    ):
        self.auto_recharg_client = auto_recharg_client

    async def auto_topup_status(self, phone_number: str, language: str) -> StatusResult:
        result_code, data_list = (
            await self.auto_recharg_client.query_autodebit_sub_status(
                phone_number, language
            )
        )

        if result_code not in [0, 208]:
            raise InvalidUpstreamResponse

        if result_code == 208:
            return StatusResult(status="de_activated", services=[])

        service = {}
        for parameter in data_list:
            item = {k.split(":")[1]: v for k, v in parameter.items()}
            if item["key"] == "ServiceDescription":
                service["recharge_type"] = item["value"].lower()
            elif item["key"] == "ThresholdID":
                service["threshold_id"] = item["value"]
            elif item["key"] == "Amount":
                service["amount"] = item["value"]
            elif item["key"] == "BankId":
                service["bank_id"] = item["value"]
            elif item["key"] == "BankNameFarsi" and language == "fa":
                service["bank_name"] = item["value"]
            elif item["key"] == "BankName" and language == "en":
                service["bank_name"] = item["value"]
            elif item["key"] == "ServiceId":
                service["service_id"] = item["value"]
            elif item["key"] in ["SERVICE_TYPE", "SubserviceType"]:
                service["service_type"] = item["value"]

        return StatusResult(status="activated", services=[AutoTopupService(**service)])

    async def deactive_service(
        self, phone_number, bank_id, service_id, threshold_id, service_type
    ):
        auto_recharge_response = await self.auto_recharg_client.deregister_service(
            phone_number, bank_id, service_id, threshold_id, service_type
        )
        return DeactiveResult(**auto_recharge_response)
