import asyncio
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends
from core.config import authorization, eia_client
from swagger import vouchers

from services import VoucherService
from core.config import get_voucher_service

router = APIRouter()


@router.get("/vouchers_history", responses=vouchers.vouchers_history_sample_responses)
async def vouchers_history(
    voucher_service: Annotated[VoucherService, Depends(get_voucher_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber past 365 days vouchers history

    Raises:

        401: If JWT token is invalid

    Returns:

        transaction_id: Represents voucher transaction id
        serial_id: Represents voucher serial id
        amount: Represents voucher amount
        date: Represents voucher date
        status: Represents voucher status(succeeded/failed/inprogress)

    """

    res = await voucher_service.voucher_history(
        phone_number=profile["phone_number"],
        customer_type=profile["customer_type"],
        cow_date=profile["mtni"]["cow_date"],
    )
    return res
