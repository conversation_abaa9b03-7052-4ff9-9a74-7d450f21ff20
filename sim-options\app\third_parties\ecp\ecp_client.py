import random
import secrets
from fastapi import HTT<PERSON><PERSON>x<PERSON>

from ngmi_http import HTT<PERSON>B<PERSON>, InvalidUpstreamResponse


class ECPClient(HTTPBase):
    def __init__(
        self, end_point, promotional_sms_cat_id, logger, http_client=None
    ) -> None:
        super().__init__(logger, http_client)
        self.end_point = end_point
        self.promotional_sms_cat_id = promotional_sms_cat_id

    async def _call_api(self, url, method, body, api_name, subject):
        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            api_name=api_name,
            service="ECP",
            subject=subject,
        )
        return response

    async def add_remove_dnd(self, phone_number, promotional_sms_status):
        action_type = 1 if promotional_sms_status else 2

        body = {
            "req_code": 313,
            "data": {
                "subscribers": [phone_number],
                "channel": "SMS",
                "transactionId": secrets.randbits(64),
                "categoryDetails": [
                    {
                        "category_id": str(self.promotional_sms_cat_id),
                        "category_name": "PromotionalSMS",
                        "sender_id": "",
                        "actiontype": action_type,
                    }
                ],
            },
        }
        response = await self._call_api(
            url=self.end_point,
            method="POST",
            body=body,
            api_name="AddRemoveDND",
            subject=phone_number,
        )
        status_code = response["data"]["data"]["response_code"]

        return {"status_code": status_code}

        if status_code in ["5121", "5123"]:
            raise HTTPException(status_code=400, detail="Something went wrong")

        elif status_code != "5000":
            raise InvalidUpstreamResponse

        return {"promotional_sms": promotional_sms_status}

    async def get_catsubcat_status(self, phone_number):
        body = {
            "req_code": 262,
            "data": {
                "msisdn": phone_number,
                "transactionId": secrets.randbits(64),
                "channel": "SMS",
            },
        }

        response = await self._call_api(
            url=self.end_point,
            method="POST",
            body=body,
            subject=phone_number,
            api_name="getCatSubCatStatus",
        )
        status_code = int(response["data"]["data"]["response_code"])
        if status_code != 5000:
            raise InvalidUpstreamResponse

        promotional_sms = next(
            (
                i
                for i in response["data"]["data"]["status_info"]
                if i["category_id"] == str(self.promotional_sms_cat_id)
            ),
            None,
        )
        if not promotional_sms:
            raise InvalidUpstreamResponse(
                "promotional sms category id doesn't exist in response"
            )

        # 1 present in dnd list and 0 for on presenting
        res = {"promotional_sms": not bool(int(promotional_sms["status"]))}
        return res
