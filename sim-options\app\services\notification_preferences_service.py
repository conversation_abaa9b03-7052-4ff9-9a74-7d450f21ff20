from datetime import datetime
from pydantic import BaseModel


class OTPProcessResult(BaseModel):
    otp_invalid: bool = False
    sms_otp: bool = False


class ChangeNotifResult(BaseModel):
    Success: bool = True


class NotificationPreferenceService:
    def __init__(self, enm_client, eia_client, push_notification_client):
        self.enm_client = enm_client
        self.eia_client = eia_client
        self.push_notification_client = push_notification_client

    async def get_customer_info(self, phone_number, fields= None):
        eia_response = await self.eia_client.get_customer_profile(phone_number, fields)
        return eia_response

    async def get_notif(self, phone_number, sim_type, fields):
        enm_response = await self.enm_client.get_notif_preferences(phone_number)

        enm_res = {
            "language": enm_response["language"].lower(),
            "sms_phone_number": (
                enm_response["preferredMsisdn"]
                if "nil" not in str(enm_response["preferredMsisdn"]).lower()
                else ""
            ),
            "email_address": (
                enm_response["emailAddress"].lower()
                if "nil" not in str(enm_response["emailAddress"]).lower()
                else ""
            ),
            "email_notification": "EMAIL" in enm_response["channel"],
            "push_notification": "APP" in enm_response["channel"],
            "sms_notification": "SMS" in enm_response["channel"],
        }

        if sim_type == "fd":
            enm_res["default_phone_number"] = phone_number
        else:
            customer_profile = await self.get_customer_info(phone_number, fields)
            enm_res["default_phone_number"] = customer_profile[
                "notification_phone_number"
            ]
        return enm_res

    async def otp_process(
        self,
        sms_phone_number,
        sms_otp,
        phone_number,
        email_address,
        email_otp,
        sim_type,
        fields,
        sms_notification,
    ):
        if (sms_phone_number and not sms_otp and sms_phone_number != phone_number) or (
            email_address and not email_otp
        ):
            return OTPProcessResult(otp_invalid=True)

        default_phone_number = phone_number
        if sim_type == "td":

            customer_profile = await self.get_customer_info(
                phone_number=phone_number, fields=fields
            )
            default_phone_number = customer_profile["notification_phone_number"]

        if (
            sms_notification
            and sms_phone_number != phone_number
            and sms_phone_number[-10:] != default_phone_number[-10:]
            and not sms_otp
        ):
            return OTPProcessResult(sms_otp=True)
        return OTPProcessResult()

    async def change_notif(
        self, phone_number, notification_preferences, language, preferred_language
    ):
        enm_response = await self.enm_client.get_notif_preferences(phone_number)

        current_notif_preferences = {
            "language": enm_response["language"].lower(),
            "sms_phone_number": (
                enm_response["preferredMsisdn"]
                if "nil" not in str(enm_response["preferredMsisdn"]).lower()
                else ""
            ),
            "email_address": (
                enm_response["emailAddress"].lower()
                if "nil" not in str(enm_response["emailAddress"]).lower()
                else ""
            ),
            "email_notification": "EMAIL" in enm_response["channel"],
            "push_notification": "APP" in enm_response["channel"],
            "sms_notification": "SMS" in enm_response["channel"],
        }

        current_notif_preferences.update(notification_preferences)

        enm_response = await self.enm_client.set_notif_preferences(
            phone_number,
            language,
            sms_phone_number=current_notif_preferences["sms_phone_number"],
            email_address=current_notif_preferences["email_address"],
            push_notification=current_notif_preferences["push_notification"],
            email_notification=current_notif_preferences["email_notification"],
            sms_notification=current_notif_preferences["sms_notification"],
        )
        res = None
        if notification_preferences.get("push_notification"):
            res = await self.push_notification_client.opt_in(
                phone_number, preferred_language
            )
        elif (
            notification_preferences.get("push_notification") is not None
            and notification_preferences.get("push_notification") == False
        ):
            res = await self.push_notification_client.opt_out(phone_number)

        if res is not None and res["error_status"]:
            return ChangeNotifResult(Success=False)

        return ChangeNotifResult(Success=True)
