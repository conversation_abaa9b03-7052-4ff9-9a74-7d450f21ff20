from pymongo import MongoClient
from ..collection import MongoCollection
from datetime import datetime


class DordaneRepository(MongoCollection):
    def __init__(self, session: MongoClient) -> None:
        super().__init__("dordaneh", session)

    async def get_dordane_list(
        self, phone_number: str, registration_date: datetime
    ) -> list:
        query = {
            "msisdn": phone_number,
            "registration_date": registration_date,
        }
        return await self.find(query)
    
    async def get_msisdn_dordane_list(
        self, phone_number: str, registration_date: datetime
    ) -> dict:
        query = {
            "msisdn": phone_number,
            "registration_date": registration_date,
        }
        return await self.find_one(query)
    
    async def exist_in_dordaneh_db(
        self, owner_msisdn:str, dordane_msisdn:str
    ):
        query = {
            "$or": [
                {"msisdn": dordane_msisdn},
                {"dordaneh":owner_msisdn},
                {"dordaneh": dordane_msisdn},
            ]
        }
        return await self.find_one(query)

    async def get_dordane(
        self, phone_number: str, registration_date: datetime, dordaneh_msisdn: str
    ):
        query = {
            "msisdn": phone_number,
            "registration_date": registration_date,
            "dordaneh": dordaneh_msisdn,
        }

        return await self.find_one(query)
    
    async def update_dordane(
        self, phone_number: str, registration_date: datetime, dordaneh_msisdn: str
    ):
        
        query= {
            "msisdn": phone_number,
            "registration_date": registration_date,
        }

        data = {"dordaneh": dordaneh_msisdn}

        return await self.update_one_by_push(query, data)
    
    async def create_new_dordane(
        self, phone_number: str, registration_date: datetime, dordaneh_msisdn: str
    ):
        query = {
            "msisdn": phone_number,
            "registration_date": registration_date,
            "dordaneh": [dordaneh_msisdn],
        }

        return await self.insert_one(query)

    async def delete_dordane(
        self, phone_number:str, registration_date:datetime, dordaneh_msisdn: str
    ):
        query={
            "msisdn": phone_number,
            "registration_date": registration_date,
            },
        data= {"dordaneh": dordaneh_msisdn}
        item = await self.update_one_by_pull(query, data)
        if item:
            return await self.delete_one({"msisdn": phone_number})
        return None