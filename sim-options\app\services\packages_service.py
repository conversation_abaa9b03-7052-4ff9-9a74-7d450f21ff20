from datetime import datetime

from utils.packages_converter_time import (
    convert_to_consistent_format,
    convert_utc_to_iran_time_str,
)


class PackagesService:
    def __init__(self, bom_client):
        self.bom_client = bom_client

    async def get_history(self, cow_date, phone_number, language):
        bom_response = await self.bom_client.bom_subscription_history(
            phone_number, language
        )

        bom_res = []
        if (
            bom_response["resultMessage"] == "Success"
            and bom_response["resultCode"] == "0"
        ):
            bom_response = bom_response["offerings"]
            offers = []
            if isinstance(bom_response, dict):
                offers.append(bom_response)
            else:
                offers.extend(bom_response)
            for offer in offers:
                bom_res.append(
                    {
                        "name": (
                            offer["boltonDescriptionFa"]
                            if language == "fa"
                            else offer["boltonDescriptionEn"]
                        ),
                        "active_date": offer["activationDate"],
                        "expiry_date": offer["expiryDate"],
                        "offer_code": offer.get("boltonCode"),
                    }
                )

        result = []
        if cow_date:
            cow_date = datetime.strptime(cow_date, "%Y-%m-%d")
            for item in bom_res[::-1]:
                activation_date = item["active_date"]
                activation_date = convert_to_consistent_format(activation_date)

                if cow_date < activation_date:
                    result.append(item)
        else:
            result = bom_res[::-1]
        response = []
        for item in result:
            item["active_date"] = convert_utc_to_iran_time_str(item["active_date"])
            item["expiry_date"] = convert_utc_to_iran_time_str(item["expiry_date"])
            response.append(item)
        return response
