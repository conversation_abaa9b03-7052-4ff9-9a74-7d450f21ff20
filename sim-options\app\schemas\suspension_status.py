from pydantic import BaseModel,field_validator
from typing import List
from datetime import datetime


class SuspensionStatus(BaseModel):
    status: bool
    reason: str

    @field_validator("reason")
    def reason_valid(cls, v):
        if v and v not in ["temporary", "stolen"]:
            raise ValueError("Reason is invalid")
        return v

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "status": True,
                    "reason": "",
                }
            ]
        }
    }

class Reason(BaseModel):
    date: datetime
    description: str
    type: str

class SuspensionResponse(BaseModel):
    is_active: bool
    reasons: List[Reason] = []