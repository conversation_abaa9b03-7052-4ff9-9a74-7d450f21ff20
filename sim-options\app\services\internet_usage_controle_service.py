from datetime import datetime
from pydantic import BaseModel


class UsageControlResult(BaseModel):
    queued: bool = False


class InternetUsageService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def usage_control(self, usage_control, phone_number, customer_type):

        if usage_control:
            if customer_type == "prepaid":
                tariff_code = "DBU"
            else:
                tariff_code = "DBU5029"
        else:
            if customer_type == "prepaid":
                tariff_code = "PACK005"
            else:
                tariff_code = "PACK001"

        eia_response = await self.eia_client.change_tariff_plan(
            phone_number, customer_type, tariff_code
        )

        if eia_response["queued"]:
            return UsageControlResult(queued=True)
        return UsageControlResult(queued=False)
