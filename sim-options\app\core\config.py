import json
from .settings import settings
from minio import Minio
from ngmi_logging import Logger
from ngmi_redis import RedisClient
from ngmi_authorization import AuthorizationJWT
from ngmi_eia import EIAClient
from ngmi_notification import NotificationClient
from third_parties import (
    AutoRechargeClient,
    RecoClient,
    ENMClient,
    PushNotificationClient,
    BOMClient,
    PushNotificationClient,
    BOMClient,
    AATClient,
    HSDPClient,
    ECPClient,
    DMSClient,
    CVMClient,
    NGPGClient,
)


logger = Logger(
    url=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG}",
    aurl=f"{settings.FLUENTD_HTTP_PROTOCOL}://{settings.FLUENTD_HTTP_ENDPOINT}:{settings.FLUENTD_HTTP_PORT}/{settings.FLUENTD_HTTP_TAG2}",
    service_name=settings.SERVICE_NAME,
    worker_id=settings.WORKER_ID,
)

redis = RedisClient(
    nodes=json.loads(settings.REDIS_CLUSTER_NODES),
    username=settings.REDIS_CLUSTER_USERNAME,
    password=settings.REDIS_CLUSTER_PASSWORD,
    prefix=settings.REDIS_PREFIX,
    logger=logger,
)

authorization = AuthorizationJWT(
    jwt_public_key=settings.JWT_PUBLIC_KEY,
    validate_refresh_token="redis_blacklist",
    redis_client=redis,
    redis_prefix=settings.AUTHORIZATION_REDIS_PREFIX,
)

auto_recharge_client = AutoRechargeClient(
    base_url=settings.AUTORECHARGE_ENDPOINT,
    auto_recharge_client_id=settings.AUTORECHARGE_CLIENT_ID,
    auto_recharge_password=settings.AUTORECHARGE_PASSWORD,
    auto_recharge_username=settings.AUTORECHARGE_USERNAME,
    logger=logger,
)

eia_client = EIAClient(
    endpoint=settings.EIA_ENDPOINT,
    username=settings.EIA_USERNAME,
    password=settings.EIA_PASSWORD,
    logger=logger,
)


notification_client = NotificationClient(
    base_url=settings.NOTIFICATION_SERVICE_ENDPOINT,
    logger=logger,
    eia_client=eia_client,
)


reco_client = RecoClient(
    endpoint=settings.BNUMBER_ENDPOINT,
    bnumber_username=settings.BNUMBER_USERNAME,
    bnumber_password=settings.BNUMBER_PASSWORD,
    eia_client=eia_client,
    logger=logger,
)

enm_client = ENMClient(
    base_url=settings.ENM_ENDPOINT,
    enm_username=settings.ENM_USERNAME,
    enm_password=settings.ENM_PASSWORD,
    logger=logger,
)

push_notification_client = PushNotificationClient(
    base_url=settings.NOTIFICATION_PUSH_SERVICE_ENDPOINT, logger=logger
)

bom_client = BOMClient(
    base_url=settings.BOM_ENDPOINT, bom_token=settings.BOM_TOKEN, logger=logger
)

aat_client = AATClient(base_url=settings.AAT_ENDPOINT, logger=logger)

hsdp_client = HSDPClient(base_url=settings.HSDP_ENDPOINT, logger=logger)

ecp_client = ECPClient(
    end_point=settings.ECP_ENDPOINT,
    promotional_sms_cat_id=settings.PROMOTIONAL_SMS_CAT_ID,
    logger=logger,
)
dms_client = DMSClient(end_point=settings.DMS_URL, logger=logger)

minio_client = Minio(
    settings.MINIO_STORAGE_ENDPOINT,
    access_key=settings.MINIO_STORAGE_ACCESS_KEY,
    secret_key=settings.MINIO_SECRET_KEY,
    secure=False,
    region=settings.MINIO_REGION,
)

cvm_client = CVMClient(
    endpoint=settings.CVM_ENDPOINT,
    username=settings.CVM_USERNAME,
    password=settings.CVM_PASSWORD,
    logger=logger,
)

ngpg_client = NGPGClient(
    base_url=settings.NGPG_ENDPOINT,
    token=settings.NGPG_TOKEN,
    mid=settings.NGPG_MID,
    ngpg_channel_app=settings.NGPG_CHANNEL_APP,
    ngpg_channel_web=settings.NGPG_CHANNEL_WEB,
    offer_map=settings.OFFER_MAP,
    logger=logger,
)

from services import TopUpService


async def get_topup_service():
    return TopUpService(auto_recharg_client=auto_recharge_client)


from services import FavoriteNumberService


async def get_favorite_numbers_service():
    return FavoriteNumberService(reco_client=reco_client, eia_client=eia_client)


from services import NotificationPreferenceService


async def get_notification_preference_service():
    return NotificationPreferenceService(
        enm_client=enm_client,
        eia_client=eia_client,
        push_notification_client=push_notification_client,
    )


from services import PackagesService


async def get_packages_service():
    return PackagesService(bom_client=bom_client)


from services import Pre2PostService


async def get_pre2post_service():
    return Pre2PostService(
        hsdp_client=hsdp_client, aat_client=aat_client, eia_client=eia_client
    )


from services import PromotionalService


async def get_promotional_service():
    return PromotionalService(ecp_client=ecp_client)


from services import RegistrationFileService


async def get_registration_file_service():
    return RegistrationFileService(dms_client=dms_client)


from services import RewardsService


async def get_rewards_service():
    return RewardsService(cvm_client=cvm_client)


from services import SummaryService


async def get_summary_service():
    return SummaryService(ecp_client=ecp_client, eia_client=eia_client)


from services import TransactionService


async def get_transaction_service():
    return TransactionService(ngpg_client=ngpg_client, eia_client=eia_client)


from services import InternetUsageService


async def get_internet_usage_control_service():
    return InternetUsageService(eia_client=eia_client)


from services import PukService


async def get_puk_service():
    return PukService(eia_client=eia_client)


from services import RoamingService


async def get_roaming_service():
    return RoamingService(eia_client=eia_client)


from services import SimUpgradeService


async def get_sim_upgrade_service():
    return SimUpgradeService(eia_client=eia_client)


from services import SuspensionService


async def get_suspension_service():
    return SuspensionService(eia_client=eia_client)


from services import VoucherService


async def get_voucher_service():
    return VoucherService(eia_client=eia_client)


from services import TtarifPlanService


async def get_tarif_plan_service():
    return TtarifPlanService(eia_client=eia_client)


from services import DordaneService


async def get_dordane_service():
    return DordaneService(eia_client=eia_client)


from services import RegisteredNumbersService


async def get_registered_numbers_service():
    return RegisteredNumbersService(eia_client=eia_client)


from services import RegisteredNumbersNewService


async def get_registered_numbers_new_service():
    return RegisteredNumbersNewService(
        eia_client=eia_client, hsdp_client=hsdp_client, aat_client=aat_client
    )


from services import RegisteredNumbersV2NewService


async def get_registered_numbers_v2_new_service():
    return RegisteredNumbersV2NewService(
        eia_client=eia_client, hsdp_client=hsdp_client, aat_client=aat_client
    )


from services import TerminationService


async def get_termination_service():
    return TerminationService(eia_client=eia_client)
