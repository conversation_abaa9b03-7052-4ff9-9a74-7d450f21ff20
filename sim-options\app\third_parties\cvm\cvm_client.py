from ngmi_http import HTT<PERSON>Base, InvalidUpstreamResponse
from httpx import Timeout
from .parser import cvm_parser
from pydantic import BaseModel
from typing import Optional

class MarketPlaceHistResult(BaseModel):
    status_code: int
    history_list: Optional[list]


class CVMClient(HTTPBase):
    def __init__(self, endpoint, username, password, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.endpoint = endpoint
        self.username = username
        self.password = password

    async def _call_api(
        self,
        url,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        timeout=None,
        request_parser=None,
    ):
        if not request_parser:
            request_parser = cvm_parser
        url = self.endpoint + url

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            timeout=timeout,
            api_name=api_name,
            subject=subject,
            service="CVM",
            request_parser=request_parser
        )

        return response

    @staticmethod
    def offer_type_parser(offer_title, data_offer_key_words):
        for key_word in data_offer_key_words:
            if key_word in offer_title:
                return "data"
        return "voice"

    async def cvm(self, phone_number):
        body = {
            "user": self.username,
            "password": self.password,
            "MSISDN": phone_number,
        }
        url = "/myirancellgetoffer"
        response = await self._call_api(
            url=url,
            method="POST",
            api_name="Cvm",
            subject=phone_number,
            body=body,
            timeout=Timeout(5.0, connect=20.0),
        )

        try:
            jresp = response["data"]
            result_status = jresp["STATUS"]
            offer_details = jresp["OFFER_DETAILS"]
        except:
            raise InvalidUpstreamResponse

        if result_status != "0":
            raise InvalidUpstreamResponse

        try:
            final_result = {}
            offer_code_list = []
            offers_data = []
            event_handler_offers_data = []
            final_result["service_flag"] = jresp.get("SERVICEFLAG", "")
            for offer in offer_details:
                offer_code_list.append(offer["OFFER_CODE"])
                offer_data = {
                    "source": "cvm",
                    "category": "smart-bundle",
                    "offer_type": self.offer_type_parser(
                        offer_title=offer["DETAIL_DESCRIPTION_V"],
                        data_offer_key_words=["GB", "MB", "Data"],
                    ),
                    "upc_code": offer["OFFER_CODE"],
                    "duration": offer["VALIDITY"],
                    "duration_unit": "day",
                    "price": offer["AMOUNT_N"],
                    "title": {
                        "fa": offer["DETAIL_DESCRIPTION_LN_V"],
                        "en": offer["DETAIL_DESCRIPTION_V"],
                    },
                    "description": {
                        "fa": offer["OFFER_DESC_LN_V"],
                        "en": offer["OFFER_DESC_V"],
                    },
                    "details": "",
                    "tags": [],
                }

                if "OFFER_CATEGORY_TYPE" in offer:
                    if offer["OFFER_CATEGORY_TYPE"] == "NORMALBOLTON":
                        offer_data["service"] = "NormalBolton"
                    elif offer["OFFER_CATEGORY_TYPE"] == "BUYABLEOFFER":
                        if offer["CS5_OFFER_TYPE"] == "0":
                            offer_data["service"] = "OnlineBuyable"
                        elif offer["CS5_OFFER_TYPE"] == "1":
                            offer_data["service"] = "BuyyableOffer"
                        else:
                            continue
                    else:
                        continue

                if "GIFT_TYPE" in offer and offer["GIFT_TYPE"] == "1":
                    offer_data["giftable"] = True
                    offer_data["giftable_code"] = offer["RELATIVE_OFFER"][
                        "RELATIVE_OFFER_CODE"
                    ]
                else:
                    offer_data["giftable"] = False

                offers_data.append(offer_data)

            final_result["offer_code_list"] = offer_code_list
            final_result["offers_data"] = offers_data

            if "EVENT_HANDLER" in jresp:
                event_handler_data = jresp["EVENT_HANDLER"]
                for event in event_handler_data:
                    if event.get("OFFER_CODE"):
                        if event["OFFER_CATEGORY_TYPE"] == "NORMALBOLTON":
                            event_handler_offer_service = "NormalBolton"
                        elif event["OFFER_CATEGORY_TYPE"] == "BUYABLEOFFER":
                            if event["CS5_OFFER_TYPE"] == "0":
                                event_handler_offer_service = "OnlineBuyable"
                            elif event["CS5_OFFER_TYPE"] == "1":
                                event_handler_offer_service = "BuyyableOffer"
                            else:
                                continue
                        else:
                            continue
                        event_data = {
                            "source": "cvm",
                            "category": "trigger",
                            "offer_type": self.offer_type_parser(
                                offer_title=event["DETAIL_DESCRIPTION_V"],
                                data_offer_key_words=["GB", "MB", "Data"],
                            ),
                            "upc_code": event["OFFER_CODE"],
                            "service": event_handler_offer_service,
                            "duration": event["VALIDITY"],
                            "duration_unit": "day",
                            "price": event["AMOUNT_N"],
                            "title": {
                                "fa": event["DETAIL_DESCRIPTION_LN_V"],
                                "en": event["DETAIL_DESCRIPTION_V"],
                            },
                            "description": {
                                "fa": event["OFFER_DESC_LN_V"],
                                "en": event["OFFER_DESC_V"],
                            },
                            "details": "",
                            "tags": [],
                        }
                        event_handler_offers_data.append(event_data)

                    elif event.get("LABELID"):
                        final_result["event_handler_label"] = {
                            "en": event["EN_LABEL_DES"],
                            "fa": event["FA_LABEL_DES"],
                        }

                if len(event_handler_offers_data) > 0:
                    final_result["event_handler_offers_data"] = (
                        event_handler_offers_data
                    )

            return final_result

        except:
            raise InvalidUpstreamResponse

    async def marketplacehist(self, phone_number):
        body = {
            "MSISDN": phone_number,
            "password": self.password,
            "user": self.username,
        }

        headers = {"Content-Type": "application/json"}

        url = "/Marketplacehist"
        response = await self._call_api(
            url=url,
            method="POST",
            api_name="Marketplacehist",
            subject=phone_number,
            headers=headers,
            body=body,
        )

        status_code = int(response["data"]["STATUS"])
        history_list = response["data"]["History"]
        return MarketPlaceHistResult(status_code= status_code, history_list= history_list)
    

        if status_code != 0:
            return []

        res = []
        for item in response["data"]["History"]:
            if int(item.get("paid_amount", "0")) > 0:
                d = {
                    "service_name": item["service_name"],
                    "activation_date": item["bonus_grant_date"],
                    "description": item["recieved_bonus"],
                }
                res.append(d)

        return res
