from datetime import datetime, timedelta
import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi import APIRouter, Depends, Query
from pymongo import MongoClient
from db.mongo.database import get_database
from db.mongo.helpers import add_batch_id, remove_batch_id
from typing import Annotated
from motor.motor_asyncio import AsyncIOMotorClient


from schemas import TerminationRequset, RemoveBatchIDRequest

from core.config import get_termination_service
from services import TerminationService

router = APIRouter()


@router.post("/create")
async def create_termination_report(
    termination_request: TerminationRequset,
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    termination_service: Annotated[
        TerminationService, Depends(get_termination_service)
    ],
):
    termination_grace_period = termination_request.termination_grace_period
    status = termination_request.status

    result = await termination_service.generate_termination_report(
        session, termination_grace_period, status
    )

    return result


@router.post(
    "/remove_batch_id",
    responses={
        400: {
            "description": "Bad Request",
            "content": {
                "application/json": {"example": {"detail": "Invalid request body."}}
            },
        },
        200: {
            "description": "Batch ID Removed",
            "content": {"example": {"message": "Batch ID removed successfully"}},
        },
    },
)
async def remove_batch_id_endpoint(
    batch_id_request: RemoveBatchIDRequest,
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    termination_service: Annotated[
        TerminationService, Depends(get_termination_service)
    ],
):
    batch_id = batch_id_request.batch_id
    status = batch_id_request.status

    result = await termination_service.remove_batch(session, batch_id, status)

    return result


@router.get(
    "/documents",
    responses={
        400: {
            "description": "Bad Request",
            "content": {
                "application/json": {
                    "example": {"detail": "Invalid request parameters."}
                }
            },
        },
        200: {
            "description": "Documents Retrieved",
            "content": {
                "example": {
                    "total_pages": 2,
                }
            },
        },
    },
)
async def get_documents(
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
    termination_service: Annotated[
        TerminationService, Depends(get_termination_service)
    ],
    batch_id: str,
    page: int = Query(1, ge=1),
    page_size: int = Query(10000, ge=1, le=100000),
):
    try:
        batch_id = uuid.UUID(batch_id, version=4)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid batch_id.")

    result = await termination_service.documents(session, batch_id, page_size, page)
    if result.invalid_page:
        raise HTTPException(status_code=400, detail="Invalid page number.")

    return result
