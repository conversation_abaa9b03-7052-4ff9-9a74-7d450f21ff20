from datetime import datetime
from core.config import ngpg_client


class TransactionService:
    def __init__(self, ngpg_client, eia_client):
        self.ngpg_client = ngpg_client
        self.eia_client = eia_client

    async def gift_history(self, phone_number, cow_date, client_name, language):
        ngpg_response = await self.ngpg_client.get_gift_transactions_history(
            phone_number, language, cow_date, client_name
        )
        offers = ngpg_response.offers
        transaction_list = ngpg_response.transactions

        res = []
        if transaction_list:
            for payment in transaction_list:
                try:
                    offer = offers[payment["offerCode"]]
                    offer_title = (
                        offer["fa_title"] if language == "fa" else offer["en_title"]
                    )
                except KeyError:
                    offer_title = payment["offerCode"]
                d = {
                    "transaction_id": payment["ipsReferenceId"],
                    "offer_title": offer_title,
                    "service": payment["service"],
                    "amount": payment["paidAmount"],
                    "payment_mode": payment["paymentMode"],
                    "date": payment["transactionDate"],
                    "beneficiary_phone_number": payment.get("serviceMsisdn"),
                    "payment_status": (
                        True if payment["paymentStatus"] == "S" else False
                    ),
                    "purchase_status": (
                        True
                        if payment["fulfillmentReferenceId"]
                        and payment["paymentStatus"] == "S"
                        else False
                    ),
                }
                res.append(d)
        return res

    async def transaction_history(self, phone_number, sim_type, cow_date, client_name):
        if sim_type == "fttx":
            customer_profile = await self.eia_client.fftx_get_subscriber_details(
                fttx_id=phone_number, fields=["notification_phone_number"]
            )
            phone_number = customer_profile["notification_phone_number"]

        ngpg_response = await self.ngpg_client.get_transactions_history(
            phone_number, cow_date, client_name, sim_type
        )

        transaction_list = ngpg_response.transactions

        res = []
        if transaction_list:
            for payment in transaction_list:
                purchase_status = None
                if (
                    payment["service"] == "WalletCashIn"
                    and payment["paymentStatus"] == "S"
                ):
                    purchase_status = True
                d = {
                    "transaction_id": payment["ipsReferenceId"],
                    "service": payment["service"],
                    "amount": payment["paidAmount"],
                    "bank_name": payment.get("bankName"),
                    "bank_reference_id": payment.get("bankReferenceId"),
                    "payment_mode": payment["paymentMode"],
                    "date": payment["transactionDate"],
                    "payment_status": (
                        True if payment["paymentStatus"] == "S" else False
                    ),
                    "purchase_status": (
                        True
                        if purchase_status
                        or (
                            payment["fulfillmentReferenceId"]
                            and payment["paymentStatus"] == "S"
                            and purchase_status is None
                        )
                        else False
                    ),
                    "service_msisdn": payment["serviceMsisdn"],
                    "beneficiaryPhoneNumber": payment["serviceMsisdn"],
                    "offer_code": payment["offerCode"],
                }

                component_li = []
                if payment.get("paymentComponentList", ""):
                    for component in payment["paymentComponentList"]:
                        component_dictionary = {
                            "payment_mode": component["paymentMode"],
                            "paid_amount": component["paidAmount"],
                        }

                        component_li.append(component_dictionary)

                d["payment_transactions"] = component_li

                res.append(d)
        return res
