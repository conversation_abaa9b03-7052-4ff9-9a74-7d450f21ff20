from datetime import datetime
from pydantic import BaseModel
import pytz
from io import BytesIO
from reportlab.pdfgen import canvas
from jose import jwt
import time
import mimetypes
from fastapi.responses import StreamingResponse

from core.settings import settings
from utils import convert_base64_to_pdf


class TokenResult(BaseModel):
    active_record: bool = True


class FileResult(BaseModel):
    file_exist: bool = True


class RegistrationFileService:
    def __init__(
        self,
        dms_client,
    ):
        self.dms_client = dms_client

    async def download_token(
        self,
        minio_client,
        phone_number,
        installation_id,
    ):
        response = await self.dms_client.get_multi_accounts(phone_number)
        return_dict = response["S:Envelope"]["S:Body"][
            "ns0:getBase64Documents4MyIrancellResponse"
        ]["return"]
        if not "record" in return_dict:
            return TokenResult(active_record=False)

        base64_file_dict_or_list = return_dict["record"]

        tz = pytz.timezone(settings.TIME_ZONE_STRING)
        timestamp = datetime.now(tz=tz).strftime("%Y%m%d")
        file_name = f"registration_form_{phone_number}_{timestamp}.pdf"
        file_path = f"files/generated/{settings.ENVIRONMENT}/{settings.SERVICE_NAME}/"
        file = file_path + file_name

        # Initialize a PDF canvas
        pdf_buffer = BytesIO()
        pdf_canvas = canvas.Canvas(pdf_buffer)

        if type(base64_file_dict_or_list) == list:
            # Iterate through each base64 file in the list
            for base64_file in base64_file_dict_or_list:
                # Decode base64 data and convert it to an image
                image_data = base64_file["base64"].encode("utf-8")

                pdf_canvas = convert_base64_to_pdf(pdf_canvas, image_data)

            # Save the PDF to a file
            pdf_canvas.save()
            pdf_buffer.seek(0)

        else:
            # Decode base64 data and convert it to an image
            image_data = base64_file_dict_or_list["base64"].encode("utf-8")

            pdf_canvas = convert_base64_to_pdf(pdf_canvas, image_data)

            # Save the PDF to a file
            pdf_canvas.save()
            pdf_buffer.seek(0)

        minio_client.put_object(
            settings.MINIO_BUCKET_NAME, file, pdf_buffer, len(pdf_buffer.getvalue())
        )

        token_payload = {
            "exp": int(time.time()) + 120,
            "file_path": file,
            "phone_number": phone_number,
            "installation_id": installation_id,
        }

        download_token = jwt.encode(
            token_payload, settings.JWT_PRIVATE_KEY, algorithm="RS256"
        )
        return {"download_token": download_token}

    async def download_file(self, token_decoded, minio_client):
        file_path = token_decoded["file_path"]

        try:
            data = BytesIO()
            obj = minio_client.get_object(settings.MINIO_BUCKET_NAME, file_path)
            for chunk in obj.stream(32 * 1024):
                data.write(chunk)

            mime_type, _ = mimetypes.guess_type(file_path)
            response = StreamingResponse(iter([data.getvalue()]), media_type=mime_type)
            minio_client.remove_object(settings.MINIO_BUCKET_NAME, file_path)
            return response
        except Exception as e:
            return FileResult(file_exist=False)
