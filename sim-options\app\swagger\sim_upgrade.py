sim_upgrade_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Suspension status",
        "content": {"application/json": {"example": {"is_eligible_4g": True}}},
    },
}

postal_validate_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "Invalid Postal Code",
        "content": {
            "application/json": {
                "example": {
                    "type": "https://my.irancell.ir/errors/authorization/postal/invalid",
                    "title": "OTP is invalid",
                }
            }
        },
    },
}
