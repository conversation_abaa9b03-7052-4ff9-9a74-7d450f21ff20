from pymongo import MongoClient
from ..collection import MongoCollection
from datetime import datetime

termination_grace_period = 1
reported_grace_period = 10


class TerminationRepository(MongoCollection):
    def __init__(self, session: MongoClient) -> None:
        super().__init__("termination_requests", session)

    async def add_batch_id(self, end_date, status, batch_id):
        query = {
            "request_datetime": {"$lte": end_date},
            "status": status,
        }
        data = {"batch_id": batch_id}
        result = await self.update_many(query, data)

        return {
            "updated_documents": result.modified_count,
        }

    async def remove_batch_id(self, batch_id, status):
        query = {"batch_id": batch_id}
        update = {"$set": {"status": status}, "$unset": {"batch_id": 1}}

        return await self.update_many_(query, update)

    async def count_bach_id(self, batch_id):
        query = {"batch_id": batch_id}

        return await self.count(query)

    async def find_docs_with_pagination(self, batch_id, skip, limit):
        query = {"batch_id": str(batch_id)}
        return await self.find_with_pagination(query, skip, limit)

    async def find_one_request(self, msisdn):
        query = {
            "msisdn": msisdn,
            "status": {"$in": ["reported", "in-progress"]},
        }
        return await self.find_one(query)

    async def get_termination_requests(self, query: dict):

        return await self.find(query)

    async def count_termination_requests(self, query: dict):

        return await self.count(query)

    async def add_termination_request(self, query: dict):

        return await self.insert_one(query)

    async def cancell_termination_request(self, phone_number, profile_phone_number):
        query = {"msisdn": phone_number, "status": "in-progress"}
        data = {"status": "cancelled", "cancelled_by": profile_phone_number}

        return await self.update_one(query, data)

    async def delink_update(self, msisdn):
        query = {"msisdn": msisdn}
        data = {"status": "delinked"}

        return await self.update_one(query, data)

    async def get_termination_status(self, msisdn):
        query = {
            "msisdn": msisdn,
            "status": {"$in": ["reported", "in-progress"]},
        }

        termination_request = await self.find_one(query)
        if termination_request:
            now = datetime.now().replace(microsecond=0, second=0)
            termination_request_date = termination_request["request_datetime"]
            if termination_request["status"] == "reported":
                if (now - termination_request_date).days > reported_grace_period:
                    termination_request["termination_status"] = "not_requested"
                else:
                    termination_request["termination_status"] = "pending"
            else:
                if (now - termination_request_date).days < termination_grace_period:
                    return "grace_period"
                else:
                    return "pending"
        else:
            return "not_requested"
