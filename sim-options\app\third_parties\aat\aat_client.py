from ngmi_http import HTTPBase
from .parser import aat_parser


class AATClient(HTTPBase):

    def __init__(self, base_url, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url

    async def _call_api(
        self,
        url,
        method,
        api_name,
        subject,
        body=None,
        headers=None,
        params=None,
        request_parser=None,
    ):
        if not request_parser:
            request_parser = aat_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            api_name=api_name,
            service="AAT",
            subject=subject,
            params=params,
            request_parser=request_parser,
        )

        return response

    async def aat_qualification_check(self, fake_id, phone_number):

        url = self.base_url + "/qualification_check" + "?msisdn=" + fake_id

        response = await self._call_api(
            url=url,
            method="POST",
            api_name="Qualification_check",
            subject=phone_number,
        )

        return response

        res = {"error_status": False}

        if response["data"]["status_message"] == "success":
            try:
                res["data"] = {
                    "amount_qualified": response["data"]["amount_qualified"],
                    "loan_balance": response["data"]["loan_balance"],
                    "code": response["data"]["code"],
                }
            except:
                res["error_status"] = True
        else:
            try:
                res["data"] = {
                    "loan_balance": response["data"]["loan_balance"],
                }
            except:
                res["error_status"] = True

        return res

    async def aat_do_advance(
        self, fake_id, aat_endpoint, txnid, amount, offer_type, phone_number
    ):

        url = (
            aat_endpoint
            + "/as/do_advance"
            + "?msisdn="
            + fake_id
            + "&txnid="
            + str(txnid)
            + "&amount="
            + str(amount)
            + "&service=APP"
            + "&type="
            + offer_type
        )

        response = await self._call_api(
            url=url,
            method="POST",
            api_name="do_advance",
            subject=phone_number,
        )

        res = {"error_status": False}

        if not (response["data"]["status"] == 0 or response["data"]["code"] == 0):
            res["error_status"] = True

        return res
