from ngmi_http import HTTPBase
from .parser import bom_parser


class BOMClient(HTTPBase):
    def __init__(self, base_url, bom_token, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.bom_token = bom_token

    async def _call_api(
        self,
        url,
        method,
        api_name,
        subject,
        body=None,
        params=None,
        headers=None,
        request_parser=None,
    ):
        if not request_parser:
            request_parser = bom_parser

        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            params=params,
            api_name=api_name,
            headers=headers,
            service="BOM",
            subject=subject,
            request_parser=request_parser,
        )

        return response

    async def bom_subscription_history(self, phone_number, language):
        headers = {
            "Authorization": self.bom_token,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

        body = {"subscriptionHistory": {"subscriber": {"msisdn": phone_number}}}

        url = self.base_url + "/bom/rest/v1/boltonActivation/subscriptionHistory"

        response = await self._call_api(
            url=url,
            method="POST",
            api_name="subscriptionHistory",
            headers=headers,
            body=body,
            subject=phone_number,
        )
        
        data = response["data"]["subscriptionHistoryResult"]
        return data
    
        res = []

        data = response["data"]["subscriptionHistoryResult"]
        if data["resultMessage"] == "Success" and data["resultCode"] == "0":
            data = data["offerings"]
            offers = []
            if isinstance(data, dict):
                offers.append(data)
            else:
                offers.extend(data)
            for offer in offers:
                res.append(
                    {
                        "name": (
                            offer["boltonDescriptionFa"]
                            if language == "fa"
                            else offer["boltonDescriptionEn"]
                        ),
                        "active_date": offer["activationDate"],
                        "expiry_date": offer["expiryDate"],
                        "offer_code": offer.get("boltonCode"),
                    }
                )
        return res[::-1]
