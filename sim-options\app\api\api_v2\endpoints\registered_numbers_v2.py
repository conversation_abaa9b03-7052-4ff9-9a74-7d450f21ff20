from typing import Any, Annotated
import json
from core.config import settings
from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import JSONResponse, Response
from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient
from core.config import authorization, eia_client
from db.mongo.database import get_database
from db.mongo.helpers import (
    update_termination_requests,
)

from schemas import (
    RegisteredNumbersInput,
    TerminationOtpRequestInput,
    TerminationSubmit,
    CancelTerminationRequestInput,
)
from utils import (
    termination_check_number,
    termination_check_entry,
    send_sms_otp,
    process_subscribers,
    list_of_eligible_subscribers,
    is_invalid_otp,
    handle_prepaid_termination,
    handle_postpaid_termination,
    handle_postpaid_cancellation,
    is_valid_msisdn,
    is_valid_phone_number,
    create_error_response,
    handle_termination_failure,
    handle_prepaid_cancellation,
)


from swagger import registered_numbers_swagger, registered_numbers_new

from services import RegisteredNumbersV2NewService
from core.config import get_registered_numbers_v2_new_service
from core.config import notification_client


router = APIRouter()


@router.post(
    "/otp/request", responses=registered_numbers_swagger.otp_request_sample_responses
)
async def request_registered_numbers_otp(
    profile: dict = Depends(authorization),
) -> Any:
    """
    Request for OTP with the intention of registered_numbers_inquiry.

    Raises:
        400: Too many OTP requests.

    Returns:
        dict: Response containing the recipient's phone number.
    """
    phone_number = profile["phone_number"]
    language = profile["language"]
    client_id = profile["client_id"]
    intention = "registered_numbers_inquiry"

    res = await send_sms_otp(phone_number, language, client_id, intention)

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post(
    "/termination/otp/request",
    responses=registered_numbers_swagger.termination_otp_request_sample_responses,
)
async def termination_otp_request(
    termination_otp: TerminationOtpRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Request for OTP with the intention of termination.

    Raises:
        400: Too many OTP requests or validation errors.

    Returns:
        dict: Response containing the recipient's phone number.
    """

    if settings.TERMINATE_OTP_EXCEPTION == "True":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/otp_exception",
                "title": "onlineTerminationExceptionTitle",
                "subTitle": "onlineTerminationExceptionSubTitle",
            },
        )

    termination_otp = termination_otp.model_dump()
    phone_number = termination_otp["phone_number"]
    national_id = termination_otp["national_id"]
    first_name = termination_otp["first_name"]
    last_name = termination_otp["last_name"]
    father_name = termination_otp["father_name"]

    profile_phone_number = profile["phone_number"]
    language = profile["language"]
    client_id = profile["client_id"]
    intention = f"termination-{phone_number}"

    termination_check_number = (
        await registered_numbers_service.termination_check_number(
            session=session,
            phone_number=phone_number,
            subscriber_phone_number=profile_phone_number,
            language=language,
        )
    )

    if termination_check_number.error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    termination_check_status = await registered_numbers_service.termination_check_entry(
        phone_number,
        national_id,
        first_name,
        last_name,
        father_name,
    )

    if termination_check_status.error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrieve phone number information.",
            },
        )

    if not termination_check_status.termination_possibility:
        return handle_termination_failure(termination_check_status.reason)

    res = await send_sms_otp(
        phone_number=profile_phone_number,
        language=language,
        client_id=client_id,
        intention=intention,
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post("/inquiry", responses=registered_numbers_swagger.inquiry_sample_responses)
async def registered_numbers(
    otp_input: RegisteredNumbersInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Retrieve subscriber registered MSISDNs.

    Raises:
        401: If JWT token is invalid.
        400: If OTP is invalid.

    Returns:
        200: List of Subscriber's registered numbers.
    """
    otp_input = otp_input.model_dump()
    otp = otp_input["otp"]
    recipient = otp_input["recipient"]
    phone_number = profile["phone_number"]

    if await is_invalid_otp(
        recipient,
        otp,
        intention="registered_numbers_inquiry",
        phone_number=phone_number,
    ):
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/authorization/otp/invalid",
            title="OTP is invalid",
        )

    result = await registered_numbers_service.inquiry(
        session=session, phone_number=phone_number
    )
    return result


@router.post(
    "/terminate/request",
    responses=registered_numbers_swagger.terminate_request_sample_response,
)
async def termination_submit_request(
    request_input: TerminationSubmit,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Submit a termination request for a given phone number.

    Raises:
        400:
            If profile type is cooperate.
            If there are more than 3 requests on the same day.
            If the entered national ID is incorrect.
            If the phone number is not active.
            If there is an active shared account.
            If there is outstanding debt.

        401: If JWT token is invalid.

    Returns:
        None
    """

    if settings.TERMINATE_OTP_EXCEPTION == "True":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/otp_exception",
                "title": "onlineTerminationExceptionTitle",
                "subTitle": "onlineTerminationExceptionSubTitle",
            },
        )

    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    national_id = request_input["national_id"]
    first_name = request_input["first_name"]
    last_name = request_input["last_name"]
    father_name = request_input["father_name"]
    otp = request_input["otp"]
    recipient = request_input["recipient"]
    reason = request_input.get("reason", "")

    subscriber_phone_number = profile["phone_number"]
    language = profile.get("language", "fa")

    customer_profile = await registered_numbers_service.get_customer_info(phone_number=phone_number)
    customer_type = customer_profile["customer_type"]
    registration_date = customer_profile["registration_date"]
    profile_type = customer_profile["profile_type"]

    if customer_type == "postpaid":
        termination_check_number_status = (
            await registered_numbers_service.termination_check_number(
                session=session,
                phone_number=phone_number,
                subscriber_phone_number=recipient,
                language=language,
            )
        )

        if termination_check_number_status.error_status:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                    "title": "Unable to retrive phone number informations.",
                },
            )

    if await is_invalid_otp(
        recipient,
        otp,
        intention=f"termination-{phone_number}",
        phone_number=subscriber_phone_number,
    ):
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/account_termination/otp/invalid",
            title="OTP is invalid",
        )

    submit_req = await registered_numbers_service.termination_submit_req(
        session=session,
        phone_number=phone_number,
        phone_customer_type = customer_type,
        phone_registration_date = registration_date,
        phone_profile_type = profile_type,
        subscriber_phone_number=subscriber_phone_number,
        national_id=national_id,
        first_name=first_name,
        last_name=last_name,
        father_name=father_name,
        reason=reason,
        language= language
    )

    if submit_req.error_status:
        if submit_req.information_lack:
            return create_error_response(
                status_code=400,
                error_type="https://my.irancell.ir/errors/account_termination/validation/failed",
                title="Unable to retrieve phone number information.",
            )
        elif not submit_req.own_number:
            return JSONResponse(
                status_code=400,
                content={
                    "type":"https://my.irancell.ir/errors/account_termination/validation/failed",
                    "title":"failed_due_not_own",
                }
            )
        elif submit_req.self_reason:
            return handle_termination_failure(submit_req.self_reason)
        elif submit_req.reason:
            return JSONResponse(
                status_code=400,
                content= {
                    "type":"https://my.irancell.ir/errors/account_termination/validation/failed",
                    "title":submit_req.reason,
                }
            )
        elif submit_req.operation_failed:
            return create_error_response(
                status_code=400,
                error_type="https://my.irancell.ir/errors/termination/request/failed",
                title="Online termination operation failed",
            )
        elif submit_req.error_message:
            return create_error_response(
                status_code=400,
                error_type="https://my.irancell.ir/errors/termination/request/failed",
                title= submit_req.error_message,
            )
        else:
            return JSONResponse(status_code=400, content= "")

    data = {
        "phone_number": phone_number,
        "subscriber_phone_number": subscriber_phone_number,
    }

    if submit_req.subscriber_details:
        for pn in submit_req.subscriber_details:
            if pn["type"] != "TDD":
                await notification_client.send_sms_notification(
                    template_id=settings.SMS_TEMPALTE_TERMINATE_REQUEST,
                    template_data=data,
                    recipient=pn["phone_number"],
                    off_net=False,
                    language=language,
                )

    return JSONResponse(status_code=200, content= "Done")


@router.get(
    "/terminate/check",
    responses=registered_numbers_swagger.terminate_check_sample_responses,
)
async def termination_check(
    phone_number: str,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Check termination possibility for a given phone number
    Raises:
        400:
            If profile type is cooperate
            If there are more than 3 requests on the same day
            If the entered The national is incorrect
            If the phone number is not active
            If there is an active shared account
            If there is outstanding debt
        401: If JWT token is invalid
    Returns:
            None
    """
    if not (len(phone_number) == 12 and phone_number.startswith("98")):
        return JSONResponse(
            status_code=422, content={"message": "Phone number is invalid"}
        )

    subscriber_phone_number = profile["phone_number"]
    language = profile["language"]

    termination_check_status = (
        await registered_numbers_service.termination_check_number(
            session=session,
            phone_number=phone_number,
            subscriber_phone_number=subscriber_phone_number,
            language=language,
        )
    )

    if termination_check_status.error_status:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if termination_check_status.termination_possibility:
        return Response(status_code=200)

    if termination_check_status.reason == "failed_due_requests":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
                "title": "SIM card termination failed due to too many requests.",
            },
        )

    if termination_check_status.reason in [
        "failed_due_sim_type",
        "failed_due_duplication",
        "failed_due_not_own",
    ]:
        raise HTTPException(status_code=400, detail=termination_check_status.reason)
    if termination_check_status.reason == "failed_due_profile_type":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
                "title": "Termination failed due to profile type.",
            },
        )

    if termination_check_status.reason == "failed_due_activation":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
                "title": "SIM card termination failed due to activation.",
                "params": {"reasons": termination_check_status.data["reasons"]},
            },
        )

    if termination_check_status.reason == "faild_due_sharedaccount":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
                "title": "SIM card termination failed due to an active shared account.",
            },
        )

    if termination_check_status.reason == "faild_due_debt":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                "title": "SIM card termination failed due to debt.",
                "params": {
                    "debt_type": termination_check_status.data["debt_type"],
                    "amount": termination_check_status.data["amount"],
                },
            },
        )


@router.post(
    "/terminate/cancel",
    responses=registered_numbers_swagger.terminate_cancel_sample_responses,
)
async def cancel_terminate_request(
    request: Request,
    request_input: CancelTerminationRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Cancel the termination request for a given phone number.

    Raises:
        401: If JWT token is invalid.

    Returns:
        None
    """
    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    profile_phone_number = profile["phone_number"]
    language = profile.get("language", "fa")


    cancell_result = await registered_numbers_service.termination_cancell(
        session= session,
        phone_number=phone_number,
        profile_phone_number= profile_phone_number
    )
    if cancell_result.error_status:
        if cancell_result.operation_failed:
            return create_error_response(
                status_code=400,
                error_type="https://my.irancell.ir/errors/termination/request/failed",
                title="Online termination operation failed",
            )
        elif cancell_result.own_number:
            raise HTTPException(status_code=400, detail="failed_due_not_own")
        else:
            return JSONResponse(status_code= 400, content= "")
        
    
    if cancell_result.data:
        sms_data = {
            "phone_number": phone_number,
            "profile_phone_number": profile_phone_number,
        }
        
        # Send SMS notifications to all relevant recipients
        for recipient in cancell_result.data:
            await notification_client.send_sms_notification(
                template_id=settings.SMS_TEMPALTE_TERMINATE_CANCEL,
                template_data=sms_data,
                phone_number=recipient,
                off_net=False,
                language=language,
            )
    


@router.post("/private_delink")
async def delink(
    request: Request,
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Delink a phone number by updating its status to "delinked".

    Args:
        request (Request): The incoming request containing the JSON payload.
        db (MongoClient): MongoDB client.

    Returns:
        dict: A response containing the status code and status message.
    """
    try:
        data = await request.json()
    except json.JSONDecodeError:
        return {"statuscode": -1, "statusmessage": "Invalid JSON payload"}

    # Validate the MSISDN
    msisdn = data.get("msisdn")
    if not msisdn:
        return {"statuscode": -1, "statusmessage": "Missing parameter msisdn"}

    if not is_valid_msisdn(msisdn):
        return {"statuscode": -1, "statusmessage": "Incorrect parameter msisdn format"}

    # Update the status in the database
    result = await registered_numbers_service.delink(
        session=session, phone_number=msisdn
    )

    # Return success response
    return {"statuscode": 0, "statusmessage": "Success"}


@router.get("/status", responses=registered_numbers_new.status_sample_responses)
async def status(
    registered_numbers_service: Annotated[
        RegisteredNumbersV2NewService, Depends(get_registered_numbers_v2_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Check termination possibility for a given phone number
    Raises:
        400:
            If profile type is cooperate
        401: If JWT token is invalid

    Returns:
            None
    """
    phone_number = profile["phone_number"]

    result = await registered_numbers_service.registered_status(
        session= session,
        phone_number=phone_number
    )
    return result