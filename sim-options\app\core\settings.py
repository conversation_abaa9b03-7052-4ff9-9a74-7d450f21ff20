import os
import secrets
from typing import Optional
import json
from files import parse_offer_mapping, parse_clients
from pydantic_settings import BaseSettings
from pydantic import HttpUrl

from uuid import uuid4

parent_dir = os.path.abspath(os.path.abspath(os.path.dirname(__file__)) + "/../")


class Settings(BaseSettings):
    API_V1_STR: str = "/v1"
    API_V2_STR: str = "/v2"
    TERMINATE_OTP_EXCEPTION: str 
    SECRET_KEY: str = secrets.token_urlsafe(32)
    SERVICE_NAME: Optional[str] = "sim-options"
    ENVIRONMENT: Optional[str] = "development"

    TERMINATE_OTP_EXCEPTION: str = "True"

    WORKER_ID: str = uuid4().hex

    MINIO_STORAGE_ENDPOINT: str
    MINIO_STORAGE_ACCESS_KEY: str
    MINIO_SECRET_KEY: str
    MINIO_BUCKET_NAME: str
    MINIO_REGION: str

    OFFER_MAP: dict = parse_offer_mapping()

    JWT_PUBLIC_KEY: str = open(f"{parent_dir}/rsa/public.pem", "r").read()
    JWT_PRIVATE_KEY: str = open(f"{parent_dir}/rsa/private.key", "r").read()

    ECP_ENDPOINT: str

    BOM_ENDPOINT: str
    BOM_TOKEN: str

    TIME_ZONE_STRING: str = "Asia/Tehran"

    DMS_URL: str

    EIA_ENDPOINT: str
    EIA_USERNAME: str
    EIA_PASSWORD: str

    ENM_ENDPOINT: str
    ENM_USERNAME: str
    ENM_PASSWORD: str

    NGPG_TOKEN: str
    NGPG_ENDPOINT: str
    NGPG_MID: str
    NGPG_CHANNEL_APP: str
    NGPG_CHANNEL_WEB: str

    CVM_ENDPOINT: str
    CVM_USERNAME: str
    CVM_PASSWORD: str

    AUTORECHARGE_USERNAME: str
    AUTORECHARGE_PASSWORD: str
    AUTORECHARGE_CLIENT_ID: str
    AUTORECHARGE_ENDPOINT: str

    BNUMBER_ENDPOINT: str
    BNUMBER_USERNAME: str
    BNUMBER_PASSWORD: str

    COMVIVA_MYIRANCELL_360_ENDPOINT: str

    HSDP_ENDPOINT: str

    AAT_ENDPOINT: str

    SENTRY_DSN: Optional[HttpUrl]

    MODERATELY_PROTECTED_URLS: list = []

    NOTIFICATION_SERVICE_ENDPOINT: str
    NOTIFICATION_PUSH_SERVICE_ENDPOINT: str

    REAUTHENTICATION_URLS: list = []

    MONGODB_URL: str

    PROMOTIONAL_SMS_CAT_ID: str

    TERMINATION_DAILY_LIMIT: str
    TERMINATION_WEEKLY_LIMIT: str

    SENTRY_TRACES_SAMPLE_RATE: str = "0.01"

    SMS_TEMPALTE_TERMINATE_CANCEL: str
    SMS_TEMPALTE_TERMINATE_REQUEST: str
    SMS_TEMPALTE_DORDANEH_REMOVE: str

    REDIS_PREFIX: str

    FLUENTD_HTTP_ENDPOINT: str
    FLUENTD_HTTP_PORT: str
    FLUENTD_HTTP_TAG: str
    FLUENTD_HTTP_TAG2: Optional[str] = "http-ngmi-additional"
    FLUENTD_HTTP_PROTOCOL: str = "http"

    REDIS_CLUSTER_NODES: str
    REDIS_CLUSTER_USERNAME: str
    REDIS_CLUSTER_PASSWORD: str

    AUTHORIZATION_REDIS_PREFIX: str

    CLIENTS_DATA: dict = parse_clients(json.load(open("files/clients.json", "r")))

    VAT_PREVIOUS: str
    VAT_CHANGETIME: str
    VAT_CURRENT: str

    class Config:
        case_sensitive = True


settings = Settings()
