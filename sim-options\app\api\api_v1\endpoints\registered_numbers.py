from datetime import datetime
from typing import Any, Annotated
from motor.motor_asyncio import AsyncIOMotorClient
import json
from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import JSONResponse, Response
from pymongo import MongoClient
from core.config import authorization, eia_client, notification_client, logger
from core.settings import settings
from db.mongo.database import get_database
from db.mongo.helpers import (
    add_termination_request,
    update_termination_request,
    update_termination_requests,
    find_one_request,
)

from schemas import (
    RegisteredNumbersInput,
    TerminationOtpRequestInput,
    TerminationSubmit,
    CancelTerminationRequestInput,
)
from utils import (
    termination_check_number,
    termination_check_entry,
)

from swagger import registered_numbers_swagger

from services import RegisteredNumbersNewService
from core.config import get_registered_numbers_new_service

router = APIRouter()
termination_grace_period = 1


@router.post(
    "/otp/request", responses=registered_numbers_swagger.otp_request_sample_responses
)
async def request_registered_numbers_otp(
    profile: dict = Depends(authorization),
) -> Any:
    """Request for otp with intention of registered_numbers_inquiry
    Raises:
        400: too many otp request
    Returns:
        None
    """

    phone_number = profile["phone_number"]

    language = profile["language"]

    res = await notification_client.send_sms_otp(
        phone_number=phone_number,
        language=language,
        intention="registered_numbers_inquiry",
        client_id=profile["client_id"],
        # sim_type=profile["sim_type"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post("/inquiry", responses=registered_numbers_swagger.inquiry_sample_responses)
async def registered_numbers(
    otp_input: RegisteredNumbersInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Retrieve subscriber registered MSISDNs

    Raises:

        401: If JWT token is invalid

    Returns:

        200: List of Subscriber's registered numbers

    """
    otp_input = otp_input.model_dump()
    otp = otp_input["otp"]
    recipient = otp_input["recipient"]

    phone_number = profile["phone_number"]

    res = await notification_client.validate_otp(
        recipient=recipient,
        otp=otp,
        intention="registered_numbers_inquiry",
        originated_msisdn=phone_number,
    )

    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    result = await registered_numbers_service.inquiry(
        session=session, phone_number=phone_number
    )
    return result


@router.get(
    "/terminate/check",
    responses=registered_numbers_swagger.terminate_check_sample_responses,
)
async def termination_check(
    phone_number: str,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Check termination possibility for a given phone number
    Raises:
        400:
            If profile type is cooperate
            If there are more than 3 requests on the same day
            If the entered The national is incorrect
            If the phone number is not active
            If there is an active shared account
            If there is outstanding debt
        401: If JWT token is invalid
    Returns:
            None
    """
    if not (len(phone_number) == 12 and phone_number.startswith("98")):
        return JSONResponse(
            status_code=422, content={"message": "Phone number is invalid"}
        )

    subscriber_phone_number = profile["phone_number"]
    language = profile["language"]

    termination_check_status = (
        await registered_numbers_service.termination_check_number(
            session=session,
            subscriber_phone_number=subscriber_phone_number,
            phone_number=phone_number,
            language=language,
        )
    )

    if termination_check_status["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if termination_check_status["termination_possibility"]:
        return Response(status_code=200)

    if termination_check_status["reason"] == "failed_due_requests":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
                "title": "SIM card termination failed due to too many requests.",
            },
        )

    if termination_check_status["reason"] in [
        "failed_due_sim_type",
        "failed_due_duplication",
        "failed_due_not_own",
    ]:
        raise HTTPException(status_code=400, detail=termination_check_status["reason"])

    if termination_check_status["reason"] == "failed_due_profile_type":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
                "title": "Termination failed due to profile type.",
            },
        )

    if termination_check_status["reason"] == "failed_due_activation":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
                "title": "SIM card termination failed due to activation.",
                "params": {"reasons": termination_check_status["data"]["reasons"]},
            },
        )

    if termination_check_status["reason"] == "faild_due_sharedaccount":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
                "title": "SIM card termination failed due to an active shared account.",
            },
        )

    if termination_check_status["reason"] == "faild_due_debt":
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                "title": "SIM card termination failed due to debt.",
                "params": {
                    "debt_type": termination_check_status["data"]["debt_type"],
                    "amount": termination_check_status["data"]["amount"],
                },
            },
        )

    return termination_check_status


@router.post(
    "/termination/otp/request",
    responses=registered_numbers_swagger.termination_otp_request_sample_responses,
)
async def termination_otp_request(
    termination_otp: TerminationOtpRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Request for otp with intention of termination
    Raises:

        400: too many otp request

    Returns:

        None

    """
    termination_otp = termination_otp.model_dump()
    phone_number = termination_otp["phone_number"]
    national_id = termination_otp["national_id"]
    first_name = termination_otp["first_name"]
    last_name = termination_otp["last_name"]
    father_name = termination_otp["father_name"]

    profile_phone_number = profile["phone_number"]
    subscriber_phone_number = profile["phone_number"]

    language = profile["language"]

    termination_check_status = (
        await registered_numbers_service.termination_check_number(
            session=session,
            subscriber_phone_number=subscriber_phone_number,
            phone_number=phone_number,
            language=language,
        )
    )

    if termination_check_status["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    termination_check_status = await registered_numbers_service.termination_check_entry(
        phone_number,
        national_id,
        first_name,
        last_name,
        father_name,
    )

    if termination_check_status["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if not termination_check_status["termination_possibility"]:
        if termination_check_status["reason"] == "failed_due_nid_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
                    "title": "Online termination operation failed due nid mismatch",
                },
            )

        if termination_check_status["reason"] == "failed_due_identity_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
                    "title": "Online termination operation failed due identity mismatch",
                },
            )

    res = await notification_client.send_sms_otp(
        phone_number=profile_phone_number,
        language=language,
        intention=f"termination-{phone_number}",
        client_id=profile["client_id"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        return {"phone_number": res["recipient"]}


@router.post(
    "/terminate/request",
    responses=registered_numbers_swagger.terminate_request_sample_response,
)
async def termination_submit_request(
    request_input: TerminationSubmit,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Submit a termination request for a given phone number
    Raises:
        400:
            If profile type is cooperate
            If there are more than 3 requests on the same day
            If the entered The national is incorrect
            If the phone number is not active
            If there is an active shared account
            If there is outstanding debt

        401: If JWT token is invalid
    Returns:
            None

    """
    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    national_id = request_input["national_id"]
    first_name = request_input["first_name"]
    last_name = request_input["last_name"]
    father_name = request_input["father_name"]
    reason = request_input["reason"]
    otp = request_input["otp"]
    recipient = request_input["recipient"]

    subscriber_phone_number = profile["phone_number"]
    language = profile["language"]

    termination_check_status_for_recipient = (
        await registered_numbers_service.termination_check_number(
            session=session,
            subscriber_phone_number=recipient,
            phone_number=phone_number,
            language=language,
        )
    )

    if termination_check_status_for_recipient["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    res = await notification_client.validate_otp(
        recipient=recipient,
        otp=otp,
        intention=f"termination-{phone_number}",
        originated_msisdn=subscriber_phone_number,
    )

    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    termination_check_status = await registered_numbers_service.termination_check_entry(
        phone_number, national_id, first_name, last_name, father_name
    )

    if termination_check_status["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                "title": "Unable to retrive phone number informations.",
            },
        )

    if not termination_check_status["termination_possibility"]:
        if termination_check_status["reason"] == "failed_due_nid_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
                    "title": "Online termination operation failed due nid mismatch",
                },
            )

        if termination_check_status["reason"] == "failed_due_identity_mismatch":
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
                    "title": "Online termination operation failed due identity mismatch",
                },
            )

    else:
        termination_check_status = (
            await registered_numbers_service.termination_check_number(
                session = session,
                subscriber_phone_number=subscriber_phone_number,
                phone_number=phone_number,
                language=language,
            )
        )

        if termination_check_status["error_status"]:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account_termination/validation/failed",
                    "title": "Unable to retrive phone number informations.",
                },
            )

        if not termination_check_status["termination_possibility"]:
            if termination_check_status["reason"] == "failed_due_requests":
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
                        "title": "SIM card termination failed due to too many requests.",
                    },
                )

            if termination_check_status["reason"] in [
                "failed_due_sim_type",
                "failed_due_duplication",
                "failed_due_not_own",
            ]:
                raise HTTPException(
                    status_code=400, detail=termination_check_status["reason"]
                )

            if termination_check_status["reason"] == "failed_due_profile_type":
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
                        "title": "Termination failed due to profile type.",
                    },
                )

            if termination_check_status["reason"] == "failed_due_activation":
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
                        "title": "SIM card termination failed due to activation.",
                        "params": {
                            "reasons": termination_check_status["data"]["reasons"]
                        },
                    },
                )

            if termination_check_status["reason"] == "faild_due_sharedaccount":
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
                        "title": "SIM card termination failed due to an active shared account.",
                    },
                )

            if termination_check_status["reason"] == "failed_due_activation":
                return JSONResponse(
                    status_code=400,
                    content={
                        "type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
                        "title": "SIM card termination failed due to debt.",
                        "params": {
                            "debt_type": termination_check_status["data"]["debt_type"],
                            "amount": termination_check_status["data"]["amount"],
                        },
                    },
                )

    res = await registered_numbers_service.termination_submit(
        session=session,
        phone_number=phone_number,
        national_id=national_id,
        subscriber_phone_number=subscriber_phone_number,
        first_name=first_name,
        last_name=last_name,
        father_name=father_name,
        reason=reason,
    )

    data = {
        "phone_number": phone_number,
        "subscriber_phone_number": subscriber_phone_number,
    }
    accept_language = profile.get("language", "fa")

    for pn in res:
        if pn["type"] != "TDD":
            await notification_client.send_sms_notification(
                template_id=settings.SMS_TEMPALTE_TERMINATE_REQUEST,
                template_data=data,
                recipient=pn["phone_number"],
                off_net=False,
                language=accept_language,
            )

    return Response(status_code=200)


@router.post(
    "/terminate/cancel",
    responses=registered_numbers_swagger.terminate_cancel_sample_responses,
)
async def cancel_terminate_request(
    request_input: CancelTerminationRequestInput,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """Cancel the termination request for a given phone number

    Raises:

        401: If JWT token is invalid

    Returns:

        None

    """
    request_input = request_input.model_dump()
    phone_number = request_input["phone_number"]
    profile_phone_number = profile["phone_number"]

    result = await registered_numbers_service.termination_cancell(
        session=session,
        phone_number=phone_number,
        profile_phone_number=profile_phone_number,
    )
    if not result.own_number:
        raise HTTPException(status_code=400, detail="failed_due_not_own")
    if result.error_status:
        await logger.error("termination_issue")

    data = {
        "phone_number": phone_number,
        "profile_phone_number": profile_phone_number,
    }
    accept_language = profile.get("language", "fa")

    for final_recipient in result.final_recipients:
        await notification_client.send_sms_notification(
            template_id=settings.SMS_TEMPALTE_TERMINATE_CANCEL,
            template_data=data,
            phone_number=final_recipient,
            off_net=False,
            language=accept_language,
        )

    return Response(status_code=200)


@router.post("/private_delink")
async def delink(
    request: Request,
    registered_numbers_service: Annotated[
        RegisteredNumbersNewService, Depends(get_registered_numbers_new_service)
    ],
    session: Annotated[AsyncIOMotorClient, Depends(get_database)],
) -> Any:
    """
    Delink
    """

    data = await request.body()
    data = json.loads(data)

    status_code = 0
    status_message = "Success"

    msisdn = data.get("msisdn")
    if not msisdn:
        status_code = -1
        status_message = "Missing parameter msisdn"
        return {"statuscode": status_code, "statusmessage": status_message}
    elif not (
        (len(msisdn) == 12 and msisdn.startswith("98"))
        or (len(msisdn) == 11 and msisdn.startswith("094"))
    ):
        status_code = -1
        status_message = "Incorrect parameter msisdn format"
        return {"statuscode": status_code, "statusmessage": status_message}

    result = await registered_numbers_service.delink(
        session=session, phone_number=msisdn
    )
    res = {"statuscode": status_code, "statusmessage": status_message}
    return res
