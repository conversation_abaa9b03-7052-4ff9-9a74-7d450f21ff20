transactions_history_sample_resonses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Transactions history list",
        "content": {
            "application/json": {
                "example": [
                    {
                        "transaction_id": "888777356012066967",
                        "service": "NormalBolton",
                        "amount": 10900.0,
                        "payment_mode": "BA",
                        "date": "2022-02-14 17:27:42",
                        "payment_status": False,
                        "purchase_status": False,
                    }
                ]
            }
        },
    },
}

transactions_history_gifts_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Gift transactions history list",
        "content": {
            "application/json": {
                "example": [
                    {
                        "transaction_id": "888777356012066967",
                        "offer_title": "Weekly 2GB",
                        "service": "GiftBolton",
                        "amount": 10900.0,
                        "payment_mode": "BA",
                        "date": "2022-02-14 17:27:42",
                        "payment_status": False,
                        "purchase_status": False,
                    }
                ]
            }
        },
    },
}
