from datetime import datetime
import asyncio


class RewardsService:
    def __init__(
        self,
        cvm_client,
    ):
        self.cvm_client = cvm_client

    async def get_marketplace(
        self,
        phone_number,
    ):

        retries = 0
        res = []
        while retries <= 10:
            try:
                cvm_res = await self.cvm_client.marketplacehist(phone_number)

                if cvm_res.status_code != 0:
                    return []

                res = []
                for item in cvm_res.history_list:
                    if int(item.get("paid_amount", "0")) > 0:
                        d = {
                            "service_name": item["service_name"],
                            "activation_date": item["bonus_grant_date"],
                            "description": item["recieved_bonus"],
                        }
                        res.append(d)
                break
            except:
                retries += 1
                asyncio.sleep(0.1)
        return res
