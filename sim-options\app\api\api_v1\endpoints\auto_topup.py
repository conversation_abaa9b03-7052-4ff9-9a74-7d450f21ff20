from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends
from fastapi.responses import JSONResponse

from core.config import authorization, auto_recharge_client

from schemas import DeActivateService

from swagger import auto_topup

from services import TopUpService
from services.topup_service import StatusR<PERSON><PERSON>, DeactiveResult
from core.config import get_topup_service


router = APIRouter()


@router.get(
    "/auto_topup",
    response_model=StatusResult,
    responses=auto_topup.auto_topup_sample_response,
)
async def auto_topup_status(
    topup_service: Annotated[TopUpService, Depends(get_topup_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> StatusResult:
    """Retrieve subscriber auto topup status

    Raises:

        401: If JWT token is invalid

    Returns:

        roaming_status
        internet_usage_control
        promotional_sms

    """
    response = await topup_service.auto_topup_status(
        profile["phone_number"], profile["language"]
    )
    return response


@router.post("/auto_topup/deactivate", responses=auto_topup.deactivate_sample_responses)
async def deactive_auto_topup(
    deactivate_service: DeActivateService,
    topup_service: Annotated[TopUpService, Depends(get_topup_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber auto topup status

    Raises:

        401: If JWT token is invalid

    Returns:

        roaming_status
        internet_usage_control
        promotional_sms

    """
    deactivate_service = deactivate_service.model_dump()
    res = await topup_service.deactive_service(
        profile["phone_number"],
        deactivate_service["bank_id"],
        deactivate_service["service_id"],
        deactivate_service["threshold_id"],
        deactivate_service["service_type"],
    )

    if res.result_code != 0:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/auto_topup/deregister/failed",
                "title": "Auto top-up deregistration failed",
            },
        )

    return {"message": "done"}
