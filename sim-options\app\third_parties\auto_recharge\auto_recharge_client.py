import random
import xmltodict
import json
import secrets
from ngmi_http import HTTPBase, InvalidUpstreamResponse
from .parser import XML

class AutoRechargeClient(HTTPBase):

    def __init__(
        self,
        base_url,
        auto_recharge_client_id,
        auto_recharge_password,
        auto_recharge_username,
        logger,
        http_client=None,
    ) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url
        self.auto_recharge_client_id = auto_recharge_client_id
        self.auto_recharge_password = auto_recharge_password
        self.auto_recharge_username = auto_recharge_username
        self.xml_processor = XML()

    async def _call_api(
        self,
        endpoint,
        payload,
        api_name,
        subject="",
        headers=None,
        subservice_name="",
        timeout=None,
        request_parser=None
    ):
        if not request_parser:
            request_parser = self.xml_processor.auto_recharge_request_parser
            
        response = await self._call_soap_api(
            url=endpoint,
            headers=headers,
            payload=payload,
            api_name=api_name,
            subservice_name=subservice_name,
            service="AUTORECHARGE",
            subject=subject,
            timeout=timeout,
            request_parser=request_parser
        )
        return json.loads(json.dumps(xmltodict.parse(response["data"])))

    async def deregister_service(
        self,
        phone_number,
        bank_id,
        service_id,
        threshold_id,
        service_type,
    ):
        phone_number = phone_number[2:]
        request_id = str(secrets.randbits(32))
        headers = {"Content-Type": "application/soap+xml", "charset": "UTF-8"}
        body = f'<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:adap="http://adapter.ws.imp.sixdee.com" xmlns:xsd="http://request.bean.ws.imp.sixdee.com/xsd" xmlns:xsd1="http://bean.ws.imp.sixdee.com/xsd">\n   <soap:Header/>\n   <soap:Body>\n      <adap:deRegisterServiceByChannel>\n         <adap:autoTopupServicesRequest>\n            <xsd:clientID>{self.auto_recharge_client_id}</xsd:clientID>\n            <xsd:extTxnId>{request_id}</xsd:extTxnId>\n\t\t\t<xsd:requestTime>******** 12:20:48</xsd:requestTime>\n            <xsd:parameters>\n               <xsd1:parameter>\n                  <xsd1:key>msisdn</xsd1:key>\n                  <xsd1:value>{phone_number}</xsd1:value>\n               </xsd1:parameter>\n\t\t\t   <xsd1:parameter>\n                  <xsd1:key>BankId</xsd1:key>\n                  <xsd1:value>{bank_id}</xsd1:value>\n               </xsd1:parameter>\n\t\t\t   <xsd1:parameter>\n                  <xsd1:key>Service_Id</xsd1:key>\n                  <xsd1:value>{service_id}</xsd1:value>\n               </xsd1:parameter>\n\t\t\t   <xsd1:parameter>\n                  <xsd1:key>Threshold_ID</xsd1:key>\n                  <xsd1:value>{threshold_id}</xsd1:value>\n               </xsd1:parameter>\n\t\t\t   \t<xsd1:parameter>\n                  <xsd1:key>SERVICE_TYPE</xsd1:key>\n                  <xsd1:value>{service_type}</xsd1:value>\n               </xsd1:parameter>\n\t\t\t   \t<xsd1:parameter>\n                  <xsd1:key>Term_Reason</xsd1:key>\n                  <xsd1:value>From_MyIrancellLDC</xsd1:value>\n               </xsd1:parameter>\n            </xsd:parameters>\n            <xsd:password>{self.auto_recharge_password}</xsd:password>\n            <xsd:username>{self.auto_recharge_username}</xsd:username>\n         </adap:autoTopupServicesRequest>\n      </adap:deRegisterServiceByChannel>\n   </soap:Body>\n</soap:Envelope>'
        url = f"{self.base_url}/QueryAutoDebitSubscriptionStatus"

        response = await self._call_api(
            endpoint=url,
            headers=headers,
            payload=body,
            api_name="deRegisterServiceByChannel",
            subject=phone_number,
        )
        data = response["soapenv:Envelope"]["soapenv:Body"][
            "ns:deRegisterServiceByChannelResponse"
        ]["ns:return"]

        result_key = next(
            (k for k in data.keys() if "resultCode" in k),
            None,
        )
        result_code = int(data[result_key])

        return {"result_code": result_code}

    async def query_autodebit_sub_status(self, phone_number, language):
        phone_number = phone_number[2:]

        headers = {"Content-Type": "application/soap+xml", "charset": "UTF-8"}
        body = f'<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:adap="http://adapter.ws.imp.sixdee.com" xmlns:xsd="http://request.bean.ws.imp.sixdee.com/xsd" xmlns:xsd1="http://bean.ws.imp.sixdee.com/xsd">\n   <soap:Header/>\n   <soap:Body>\n      <adap:getSubscriberStatus>\n         <adap:request>\n            <xsd:clientID>{self.auto_recharge_client_id}</xsd:clientID>\n            <xsd:extTxnId>200060101</xsd:extTxnId>\n            <xsd:parameters>\n               <xsd1:parameter>\n                  <xsd1:key>msisdn</xsd1:key>\n                  <xsd1:value>{phone_number}</xsd1:value>\n               </xsd1:parameter>\n            </xsd:parameters>\n            <xsd:username>{self.auto_recharge_username}</xsd:username>\n            <xsd:password>{self.auto_recharge_password}</xsd:password>\n            <xsd:requestTime>******** 16:00:36</xsd:requestTime>\n         </adap:request>\n      </adap:getSubscriberStatus>\n   </soap:Body>\n</soap:Envelope>'
        url = f"{self.base_url}/QueryAutoDebitSubscriptionStatus"
        response = await self._call_api(
            endpoint=url,
            payload=body,
            api_name="QueryAutoDebitSubscriptionStatus",
            subject=phone_number,
            headers=headers,
        )

        data = response["soapenv:Envelope"]["soapenv:Body"][
            "ns:getSubscriberStatusResponse"
        ]["ns:return"]

        result_key = next(
            (k for k in data.keys() if "resultCode" in k),
            None,
        )
        result_code = int(data[result_key])

        return result_code, data

        if result_code not in [208, 0]:
            raise InvalidUpstreamResponse

        if result_code == 208:
            return {"status": "de_activated", "services": []}

        res = {"status": "activated", "services": []}

        main_parameters_key = next(
            (k for k in data.keys() if "parameters" in k),
            None,
        )
        parameters_key = next(
            (k for k in data[main_parameters_key].keys() if "parameter" in k),
            None,
        )

        d = {"status": "activated"}
        for parameter in data[main_parameters_key][parameters_key]:
            item = {k.split(":")[1]: v for k, v in parameter.items()}
            if item["key"] == "ServiceDescription":
                d["recharge_type"] = item["value"].lower()
            if item["key"] == "ThresholdID":
                d["threshold_id"] = item["value"]
            if item["key"] == "Amount":
                d["amount"] = item["value"]
            if item["key"] == "BankId":
                d["bank_id"] = item["value"]
            if item["key"] == "BankNameFarsi" and language == "fa":
                d["bank_name"] = item["value"]
            if item["key"] == "BankName" and language == "en":
                d["bank_name"] = item["value"]
            if item["key"] == "ServiceId":
                d["service_id"] = item["value"]
            if item["key"] in ["SERVICE_TYPE", "SubserviceType"]:
                d["service_type"] = item["value"]
        res["services"].append(d)

        return res
