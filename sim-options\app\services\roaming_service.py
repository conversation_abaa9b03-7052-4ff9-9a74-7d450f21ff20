from datetime import datetime
from pydantic import BaseModel


class RoamingResult(BaseModel):
    status_code: int = 0


class RoamingService:
    def __init__(self, eia_client):
        self.eia_client = eia_client

    async def set_network(self, status, customer_type, phone_number):
        if status:
            eia_roaming = "true"
            if customer_type == "prepaid":
                eia_service_code = "7001409"
            else:
                eia_service_code = "7001407"
        else:
            eia_roaming = "false"
            if customer_type == "prepaid":
                eia_service_code = "7001408"
            else:
                eia_service_code = "7001406"

        res = await self.eia_client.set_network_services(
            phone_number=phone_number,
            service_code=eia_service_code,
            eia_roaming=eia_roaming,
        )

        if res["status_code"] == -1:
            return RoamingResult(status_code=-1)
        elif res["status_code"] == 11101:
            return RoamingResult(status_code=11101)

        return RoamingResult(status_code=0)
