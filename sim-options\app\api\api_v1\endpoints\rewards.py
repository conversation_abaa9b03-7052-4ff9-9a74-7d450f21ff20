from typing import Any, Annotated
import asyncio
from fastapi import APIRouter
from fastapi import Depends
from core.config import authorization, cvm_client
from swagger import rewards

from services import RewardsService
from core.config import get_rewards_service


router = APIRouter()


@router.get("/reward_history", responses=rewards.reward_history_sample_responses)
async def reward_history(
    rewards_service: Annotated[RewardsService, Depends(get_rewards_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber sim puks

    Raises:

        401: If JWT token is invalid

    Returns:

        service_name: Defines reward service name
        activation_date: Defines reward activation date
        description: Defines reward description

    """
    res = await rewards_service.get_marketplace(profile["phone_number"])

    return res
