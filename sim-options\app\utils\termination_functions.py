from datetime import datetime
from typing import Any
import json
import asyncio
from fastapi import APIRouter, Depends, Request, HTTPException
from fastapi.responses import JSONResponse, Response
from pymongo import MongoClient
from core.config import authorization, eia_client, notification_client, logger
from core.settings import settings
from db.mongo.database import get_database
from db.mongo.helpers import (
    add_termination_request,
    update_termination_request,
    update_termination_requests,
    find_one_request,
)


from utils import (
    termination_check_number,
    termination_check_entry,
)

termination_grace_period = 1
registered_numbers_type_map = {"LT": "Voice", "NORML": "Voice", "TDD": "TDD"}





async def send_sms_otp(phone_number: str, language: str, client_id: str, intention: str) -> dict:
    """
    Send SMS OTP to the given phone number.

    Args:
        phone_number (str): The recipient's phone number.
        language (str): The language for the OTP message.
        client_id (str): The client ID.
        intention (str): The purpose of the OTP.

    Returns:
        dict: The response from the notification client.
    """
    res = await notification_client.send_sms_otp(
        phone_number=phone_number,
        language=language,
        intention=intention,
        client_id=client_id,
    )
    return res


def process_prepaid_subscribers(subscriber_details: list) -> list:
    """
    Process prepaid subscriber details.

    Args:
        subscriber_details (list): List of subscriber details.

    Returns:
        list: Processed list of prepaid subscribers.
    """
    phone_numbers = []
    result = []

    for subscriber_detail in subscriber_details:
        if (subscriber_detail["phone_number"] not in phone_numbers and
                subscriber_detail["status"] in ["Active", "Soft-suspended", "Suspended", "waiting-for-first-callback"]):
            phone_numbers.append(subscriber_detail["phone_number"])
            result.append({
                "phone_number": subscriber_detail["phone_number"],
                "status": subscriber_detail["status"],
                "type": registered_numbers_type_map.get(subscriber_detail["type"], ""),
            })

    return result



async def process_subscribers(subscriber_details: list, db: MongoClient) -> list:
    """
    Process postpaid subscriber details.

    Args:
        subscriber_details (list): List of subscriber details.
        db (MongoClient): MongoDB client.

    Returns:
        list: Processed list of postpaid subscribers.
    """
    phone_numbers = []
    result = []
    type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}

    for pn in subscriber_details:
        if (pn["phone_number"] not in phone_numbers and
                pn["status"] in ["Active", "Soft-suspended", "Suspended", "waiting-for-first-callback"]):
            pn["type"] = type_map.get(pn["type"])
            phone_numbers.append(pn["phone_number"])
            msisdn = pn["phone_number"]

            if pn["type"] == "TDD":
                pn["termination_status"] = "not_eligible"
            else:
                termination_status = await get_termination_status(msisdn, db)
                pn["termination_status"] = termination_status

            result.append(pn)

    return result


async def get_termination_status(msisdn: str, db: MongoClient) -> str:
    """
    Get the termination status for a given MSISDN.

    Args:
        msisdn (str): The MSISDN to check.
        db (MongoClient): MongoDB client.

    Returns:
        str: The termination status.
    """
    query = {
        "msisdn": msisdn,
        "status": {"$in": ["reported", "in-progress"]},
    }
    termination_request = await find_one_request(query, db)

    if termination_request:
        if termination_request["status"] == "reported":
            return "pending"
        else:
            now = datetime.now().replace(microsecond=0, second=0)
            termination_request_date = termination_request["request_datetime"]
            if (now - termination_request_date).days < termination_grace_period:
                return "grace_period"
            else:
                return "pending"
    else:
        return "not_requested"    
      

async def is_invalid_otp(recipient: str, otp: str, intention: str ,phone_number: str) -> bool:
    """
    Validate the OTP.

    Args:
        recipient (str): The recipient's phone number.
        otp (str): The OTP to validate.
        intention (str): The intention of the OTP.

    Returns:
        bool: True if the OTP is invalid, False otherwise.
    """
    res = await notification_client.validate_otp(
        recipient=recipient,
        otp=otp,
        intention=intention,
        originated_msisdn=phone_number
    )
    return res["error_status"]


async def handle_prepaid_termination(
    phone_number: str,
    national_id: str,
    first_name: str,
    last_name: str,
    father_name: str,
    subscriber_phone_number: str,
) -> Any:
    """
    Handle prepaid termination request.

    Args:
        phone_number (str): The phone number to terminate.
        national_id (str): The national ID of the subscriber.
        first_name (str): The first name of the subscriber.
        last_name (str): The last name of the subscriber.
        father_name (str): The father's name of the subscriber.
        subscriber_phone_number (str): The subscriber's phone number.

    Returns:
        Any: The response or error.
    """
    subscribers_registered_numbers = await eia_client.list_of_subsciber_details(
        national_id, subscriber_phone_number
    )
    own_number = any(phone_number == registered_number["phone_number"] for registered_number in subscribers_registered_numbers)

    if not own_number:
        return Response(status_code=400)

    termination_check_status = await termination_check_entry(
        phone_number, national_id, first_name, last_name, father_name
    )

    if termination_check_status["error_status"]:
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/account_termination/validation/failed",
            title="Unable to retrieve phone number information.",
        )

    if not termination_check_status["termination_possibility"]:
        return handle_termination_failure(termination_check_status["reason"])

    res = await eia_client.online_termination_request(
        subscriber_code=subscriber_phone_number, phone_number=phone_number
    )
    if res["error_status"]:
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/termination/request/failed",
            title="Online termination operation failed",
        )

    return Response(status_code=200)


async def handle_postpaid_termination(
    phone_number: str,
    national_id: str,
    first_name: str,
    last_name: str,
    father_name: str,
    subscriber_phone_number: str,
    language: str,
    reason: str,
    customer_type: str,
    profile_type: str,
    registration_date: str,
    db: MongoClient,
) -> Any:
    """
    Handle postpaid termination request.

    Args:
        phone_number (str): The phone number to terminate.
        national_id (str): The national ID of the subscriber.
        first_name (str): The first name of the subscriber.
        last_name (str): The last name of the subscriber.
        father_name (str): The father's name of the subscriber.
        subscriber_phone_number (str): The subscriber's phone number.
        language (str): The language for notifications.
        db (MongoClient): The MongoDB client.

    Returns:
        Any: The response or error.
    """
    termination_check_status = await termination_check_entry(
        phone_number, national_id, first_name, last_name, father_name
    )

    if termination_check_status["error_status"]:
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/account_termination/validation/failed",
            title="Unable to retrieve phone number information.",
        )

    if not termination_check_status["termination_possibility"]:
        
        if termination_check_status["reason"] in [
                "failed_due_sim_type",
                "failed_due_duplication",
                "failed_due_not_own",
            ]:
                raise HTTPException(
                    status_code=400, detail=termination_check_status["reason"]
                )
        
        return handle_termination_failure(termination_check_status["reason"])

    termination_check_status = await termination_check_number(
        phone_number, subscriber_phone_number, language, db
    )

    if termination_check_status["error_status"]:
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/account_termination/validation/failed",
            title="Unable to retrieve phone number information.",
        )

    if not termination_check_status["termination_possibility"]:
        return handle_termination_failure(termination_check_status["reason"])

    now = datetime.now().replace(microsecond=0)
    await add_termination_request(
        {
            "msisdn": phone_number,
            "registration_date": registration_date,
            "profile_type": profile_type,
            "customer_type": customer_type,
            "request_datetime": now,
            "national_id": national_id,
            "requested_by": subscriber_phone_number,
            "cancelled_by": "",
            "first_name": first_name,
            "last_name": last_name,
            "father_name": father_name,
            "reason": reason,
            "status": "in-progress",
        },
        db,
    )

    data = {
        "phone_number": phone_number,
        "subscriber_phone_number": subscriber_phone_number,
    }

    try:
        res = await eia_client.list_of_subsciber_details(national_id, phone_number)
        for pn in res:
            if pn["type"] != "TDD":
                await notification_client.send_sms_notification(
                    template_id=settings.SMS_TEMPALTE_TERMINATE_REQUEST,
                    template_data=data,
                    recipient=pn["phone_number"],
                    off_net=False,
                    language=language,
                )
    except Exception as e:
        await logger.error(f"termination_issue: {str(e)}")

    return Response(status_code=200)


async def handle_postpaid_cancellation(
    phone_number: str,
    profile_phone_number: str,
    customer_profile: dict,
    profile: dict,
    db: MongoClient,
) -> Any:
    """
    Handle cancellation of postpaid termination request.

    Args:
        phone_number (str): The phone number to cancel termination for.
        profile_phone_number (str): The subscriber's phone number.
        customer_profile (dict): The customer profile.
        profile (dict): The user profile.
        db (MongoClient): MongoDB client.

    Returns:
        Any: The response or error.
    """
    # Update termination request status in the database
    query = {"msisdn": phone_number, "status": "in-progress"}
    data = {"$set": {"status": "cancelled", "cancelled_by": profile_phone_number}}
    if not await update_termination_request(query, data, db):
        return Response(status_code=400)

    try:
        # Retrieve subscriber details
        res = await eia_client.list_of_subsciber_details(customer_profile["national_id"], phone_number)

        final_recipients = []
        own_number = False
        for pn in res:
            if profile_phone_number == pn["phone_number"]:
                own_number = True
            if pn["type"] != "TDD":
                final_recipients.append(pn["phone_number"])

        if not own_number:
            raise HTTPException(status_code=400, detail="failed_due_not_own")

        # Prepare SMS notification data
        sms_data = {
            "phone_number": phone_number,
            "profile_phone_number": profile_phone_number,
        }
        language = profile.get("language", "fa")

        # Send SMS notifications to all relevant recipients
        for recipient in final_recipients:
            await notification_client.send_sms_notification(
                template_id=settings.SMS_TEMPALTE_TERMINATE_CANCEL,
                template_data=sms_data,
                phone_number=recipient,
                off_net=False,
                language=language,
            )
    except Exception as e:
        await logger.error(f"termination_issue: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

    return Response(status_code=200)        


def is_valid_msisdn(msisdn: str) -> bool:
    """
    Validate the MSISDN format.

    Args:
        msisdn (str): The MSISDN to validate.

    Returns:
        bool: True if the MSISDN is valid, False otherwise.
    """
    return (len(msisdn) == 12 and msisdn.startswith("98")) or (
        len(msisdn) == 11 and msisdn.startswith("094")
    )
 
def is_valid_phone_number(phone_number: str) -> bool:
    """
    Validate the phone number format.

    Args:
        phone_number (str): The phone number to validate.

    Returns:
        bool: True if the phone number is valid, False otherwise.
    """
    return len(phone_number) == 12 and phone_number.startswith("98")


def create_error_response(
    status_code: int, 
    error_type: str, 
    title: str, 
    **kwargs
) -> JSONResponse:
    """
    Create a standardized error response.

    Args:
        status_code (int): HTTP status code.
        error_type (str): Error type URL.
        title (str): Error title.
        **kwargs: Additional parameters for the error response (optional).

    Returns:
        JSONResponse: The error response.
    """
    content = {"type": error_type, "title": title}
    if kwargs:
        content["params"] = kwargs
    return JSONResponse(status_code=status_code, content=content)


def handle_termination_failure(reason: str, termination_check_status: dict = None) -> JSONResponse:
    """
    Handle termination failure based on the reason.

    Args:
        reason (str): The reason for termination failure.
        termination_check_status (dict): The termination check status containing additional data (optional).

    Returns:
        JSONResponse: The error response.
    """
    error_mappings = {
        "failed_due_nid_mismatch": {
            "error_type": "https://my.irancell.ir/errors/termination/validation/failed_due_nid_mismatch",
            "title": "Online termination operation failed due to NID mismatch.",
        },
        "failed_due_identity_mismatch": {
            "error_type": "https://my.irancell.ir/errors/termination/validation/failed_due_identity_mismatch",
            "title": "Online termination operation failed due to identity mismatch.",
        },
        "failed_due_requests": {
            "error_type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_requests",
            "title": "SIM card termination failed due to too many requests.",
        },
        "failed_due_profile_type": {
            "error_type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_profile_type",
            "title": "Termination failed due to profile type.",
        },
        "failed_due_activation": {
            "error_type": "https://my.irancell.ir/errors/account_termination/validation/failed_due_activation",
            "title": "SIM card termination failed due to activation.",
            "reasons": termination_check_status["data"]["reasons"] if termination_check_status else None,
        },
        "faild_due_sharedaccount": {
            "error_type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_sharedaccount",
            "title": "SIM card termination failed due to an active shared account.",
        },
        "faild_due_debt": {
            "error_type": "https://my.irancell.ir/errors/account_termination/validation/faild_due_debt",
            "title": "SIM card termination failed due to debt.",
            "debt_type": termination_check_status["data"]["debt_type"] if termination_check_status else None,
            "amount": termination_check_status["data"]["amount"] if termination_check_status else None,
        },
    }

    if reason in error_mappings:
        error_details = error_mappings[reason]
        # Remove None values from the error details
        error_details = {k: v for k, v in error_details.items() if v is not None}
        return create_error_response(400, **error_details)
    else:
        return create_error_response(
            400,
            error_type="https://my.irancell.ir/errors/termination/validation/failed",
            title="Termination failed due to an unknown reason.",
        )
         

async def handle_prepaid_cancellation(phone_number: str, profile_phone_number: str, nid:str) -> Any:
    """
    Handle cancellation of prepaid termination request.

    Args:
        phone_number (str): The phone number to cancel termination for.
        profile_phone_number (str): The subscriber's phone number.

    Returns:
        Any: The response or error.
    """    
    subscribers_registered_numbers = await eia_client.list_of_subsciber_details(nid, profile_phone_number)

    # Check if the phone number belongs to the subscriber
    if not any(phone_number == registered_number["phone_number"] for registered_number in subscribers_registered_numbers):
        return Response(status_code=400)

    # Cancel online termination
    res = await eia_client.cancel_online_termination(subscriber=profile_phone_number, phone_number=phone_number)
    if res["error_status"]:
        return create_error_response(
            status_code=400,
            error_type="https://my.irancell.ir/errors/termination/request/failed",
            title="Online termination operation failed",
        )

    return Response(status_code=200)


async def handle_prepaid_get_status(subscriber_details: list) -> list:

    phone_numbers = []
    for subsciber_detail in subscriber_details:
        if (
            not subsciber_detail["phone_number"] in phone_numbers
            and subsciber_detail["status"]
            in [
                "Active",
                "Soft-suspended",
                "Suspended",
                "waiting-for-first-callback",
            ]
            and subsciber_detail["type"] != "TDD"
        ):
            phone_numbers.append(subsciber_detail["phone_number"])

    tasks = []
    for phone_number in phone_numbers:
        tasks.append(eia_client.pending_online_termination_request(phone_number))

    res = []
    tasks_result = await asyncio.gather(*tasks, return_exceptions=True)
    for task_result in tasks_result:
        if not isinstance(task_result, Exception):
            if not task_result["error_status"]:
                data = task_result["data"]
                res.append(
                    {
                        "phone_number": data["phone_number"],
                        "termination_status": data["termination_status"],
                    }
                )

    return res


async def list_of_eligible_subscribers(subscriber_details: list, db: MongoClient) -> list:
    """
    Process postpaid subscriber details.

    Args:
        subscriber_details (list): List of subscriber details.
        db (MongoClient): MongoDB client.

    Returns:
        list: Processed list of postpaid subscribers.
    """
    phone_numbers = []
    pre_paid_phone_numbers = []
    result = []
    type_map = {"LT": "Voice", "TDD": "TDD", "NORML": "Voice"}

    for pn in subscriber_details:
        if (pn["phone_number"] not in phone_numbers and
                pn["status"] in ["Active", "Soft-suspended", "Suspended", "waiting-for-first-callback"]  and
                 pn["type"] != "TDD" ) :
            pn["type"] = type_map.get(pn["type"])
            phone_numbers.append(pn["phone_number"])
            msisdn = pn["phone_number"]
            customer_profile = await eia_client.get_customer_profile(phone_number=msisdn)
            customer_type = customer_profile["customer_type"]
            if customer_type == "prepaid":
               pre_paid_phone_numbers.append(pn["phone_number"])
            else:
              termination_status = await get_termination_status(msisdn, db)
              pn["termination_status"] = termination_status
              result.append(pn)
              
    tasks = []
    for pre_paid_phone_number in pre_paid_phone_numbers:
        tasks.append(eia_client.pending_online_termination_request(pre_paid_phone_number))
                  
    tasks_result = await asyncio.gather(*tasks, return_exceptions=True)  
    for task_result in tasks_result:    
        if not isinstance(task_result, Exception):
            if not task_result["error_status"]:
                data = task_result["data"]
                result.append(
                    {
                        "phone_number": data["phone_number"],
                        "termination_status": data["termination_status"],
                    }
                )   



    return result

 

 