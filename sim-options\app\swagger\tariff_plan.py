get_tariff_plan_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Current and eligible tariff plans",
        "content": {
            "application/json": {
                "example": {
                    "current_tariff_plan": {
                        "code": "DBU5029",
                        "name": "طرح کنترل مصرف آزاد",
                    },
                    "eligible_tariff_plans": [
                        {
                            "id": "BST7",
                            "name": "Tarhe Karafarinan 30Rooze",
                            "description": "Tarhe Karafarinan 30Rooze",
                        },
                        {
                            "id": "FLXISMS",
                            "name": "Payamake Pelekani Mahaneh",
                            "description": "NA",
                        },
                    ],
                }
            }
        },
    },
}


post_tariff_plan_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    400: {
        "description": "in-process/unchange tariif plan",
        "content": {
            "application/json": {
                "example": [
                    {
                        "type": "https://my.irancell.ir/errors/account/plan_change/in_process",
                        "title": "There is already a change tariff plan request in-process",
                        "message": "There is already a change tariff plan request in-process.",
                    },
                    {
                        "type": "https://my.irancell.ir/errors/account/plan_change/too_many",
                        "title": "There was a error in changing tariff plan request.",
                        "message": "There was an error in changing tariff plan request, please ask support form online chat",
                    },
                ]
            }
        },
    },
    200: {
        "description": "Change tariff plan",
        "content": {"application/json": {"example": {"message": "done"}}},
    },
}


tariff_plan_history_sample_responses = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Tariff plan history",
        "content": {
            "application/json": {
                "example": {
                    "current_tariff_plan": {
                        "code": "DBU5029",
                        "name": "طرح کنترل مصرف آزاد",
                    },
                    "eligible_tariff_plans": [
                        {
                            "id": "BST7",
                            "name": "Tarhe Karafarinan 30Rooze",
                            "description": "Tarhe Karafarinan 30Rooze",
                        },
                        {
                            "id": "FLXISMS",
                            "name": "Payamake Pelekani Mahaneh",
                            "description": "NA",
                        },
                    ],
                }
            }
        },
    },
}
