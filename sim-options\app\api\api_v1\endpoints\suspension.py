from typing import Any, Annotated
from fastapi import APIRouter
from fastapi import Depends
from fastapi import HTTPException
from fastapi.responses import JSONResponse
from core.config import authorization, eia_client
from schemas import SuspensionStatus
from swagger import suspension

from services import SuspensionService
from core.config import get_suspension_service
from services.suspension_reason_service import SuspensionResult, UpdateSuspensionResult

router = APIRouter()


@router.get(
    "/suspension_status", responses=suspension.get_suspension_status_sample_responses
)
async def suspension_status(
    suspension_service: Annotated[SuspensionService, Depends(get_suspension_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber suspension status

    Raises:

        401: If JWT token is invalid

    Returns:

        status: Represents suspension status

    """

    line_status = await suspension_service.get_status(
        phone_number=profile["phone_number"], customer_type=profile["customer_type"]
    )

    return {"status": line_status["suspension_status"], "date": line_status["date"]}


@router.post(
    "/suspension_status", responses=suspension.post_suspension_status_sample_responses
)
async def update_suspension_status(
    suspension_status: SuspensionStatus,
    suspension_service: Annotated[SuspensionService, Depends(get_suspension_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber suspension status

    Raises:

        401: If JWT token is invalid

    Returns:

        message: Represents operation status

    """
    suspension_status = suspension_status.model_dump()

    result: UpdateSuspensionResult = await suspension_service.update_suspension(
        phone_number=profile["phone_number"],
        reason=suspension_status["reason"],
        customer_type=profile["customer_type"],
        status=suspension_status["status"],
    )

    if result.soft_error_status:
        if result.soft_in_progress:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account/suspention/failed/in_progress",
                    "title": "There was a error in account suspension request",
                    "detail": "Requesting account suspension failed for a in progress request",
                    "message": "There was an error in suspending SIM request, please ask support form online chat",
                },
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account/suspention/failed/unknown",
                    "title": "There was a error in account suspension request",
                    "detail": "Requesting account suspension failed for unknown reason",
                    "message": "There was an error in suspending SIM request, please ask support form online chat",
                },
            )
    if result.suspension_status:
        raise HTTPException(status_code=400, detail="Sim is not suspended")
    if result.suspension_date:
        raise HTTPException(status_code=400, detail="Missing suspension date")
    if result.revoke_error_status:
        if result.revoke_in_progress:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account/revoke_suspention/failed/in_progress",
                    "title": "There was a error in account sus revoke request",
                    "detail": "Requesting account suspension revoke request failed for a in progress request",
                    "message": "There was an error in revoking the SIM suspension, please ask support form online chat",
                },
            )
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/account/revoke_suspention/failed/unknown",
                    "title": "There was a error in account sus revoke request",
                    "detail": "Requesting account suspension revoke request failed for unknown reason",
                    "message": "There was an error in revoking the SIM suspension, please ask support form online chat",
                },
            )
