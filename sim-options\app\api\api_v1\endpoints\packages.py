from datetime import datetime
from typing import Any, Annotated

from fastapi import APIRouter
from fastapi import Depends


from core.config import authorization, bom_client

from swagger import packages
from utils.packages_converter_time import (
    convert_to_consistent_format,
    convert_utc_to_iran_time_str,
)

from services import PackagesService
from core.config import get_packages_service

router = APIRouter()


@router.get("/packages_history", responses=packages.packages_history_sample_response)
async def packages_history(
    packages_service: Annotated[PackagesService, Depends(get_packages_service)],
    profile: Annotated[dict, Depends(authorization)],
):

    res = await packages_service.get_history(
        profile["mtni"]["cow_date"], profile["phone_number"], profile["language"]
    )

    return res
