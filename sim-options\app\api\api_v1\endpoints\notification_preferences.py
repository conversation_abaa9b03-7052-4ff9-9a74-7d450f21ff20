from typing import Any, Annotated

from fastapi import APIRouter, Request
from fastapi import Depends
from fastapi import HTT<PERSON>Ex<PERSON>
from fastapi.responses import JSONResponse, Response

from core.config import (
    authorization,
    enm_client,
    eia_client,
    notification_client,
    push_notification_client,
)

from schemas import NotificationPreferences
from schemas import NotificationPreferencesOTP

from swagger import notification_preferences

from services import NotificationPreferenceService
from core.config import get_notification_preference_service
from services.notification_preferences_service import (
    ChangeNotifResult,
    OTPProcessResult,
)

router = APIRouter()


@router.get(
    "", responses=notification_preferences.get_notification_preferences_sample_response
)
async def get_notification_preferences(
    notif_preference_service: Annotated[
        NotificationPreferenceService, Depends(get_notification_preference_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    res = await notif_preference_service.get_notif(
        profile["phone_number"], profile["sim_type"], ["notification_phone_number"]
    )
    return res


@router.patch(
    "",
    responses=notification_preferences.patch_notification_preferences_sample_response,
)
async def change_notification_preferences(
    request: Request,
    notification_preferences: NotificationPreferences,
    notif_preference_service: Annotated[
        NotificationPreferenceService, Depends(get_notification_preference_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    notification_preferences = notification_preferences.dict(exclude_unset=True)
    sms_otp = notification_preferences.get("sms_otp")
    email_otp = notification_preferences.get("email_otp")
    sms_phone_number = notification_preferences.get("sms_phone_number")
    sms_notification = notification_preferences.get("sms_notification")
    email_address = notification_preferences.get("email_address")

    otp_process: OTPProcessResult = await notif_preference_service.otp_process(
        sms_phone_number=sms_phone_number,
        sms_otp=sms_otp,
        phone_number=profile["phone_number"],
        email_address=email_address,
        email_otp=email_otp,
        sim_type=profile["sim_type"],
        fields=["notification_phone_number"],
        sms_notification=sms_notification,
    )

    if otp_process.otp_invalid:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    if otp_process.sms_otp:
        raise HTTPException(status_code=400, detail="SMS OTP is required.")

    if sms_otp and sms_phone_number != profile["phone_number"]:

        sms_otp_status = await notification_client.validate_otp(
            recipient=notification_preferences["sms_phone_number"],
            intention="options_sms_notification",
            otp=sms_otp,
            originated_msisdn=profile["phone_number"],
        )

        if sms_otp_status["error_status"]:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                    "title": "OTP is invalid",
                },
            )

    if email_otp:

        email_otp_status = await notification_client.validate_otp(
            recipient=email_address,
            intention="options_email_notification",
            otp=email_otp,
            originated_msisdn=profile["phone_number"],
        )

        if email_otp_status["error_status"]:
            return JSONResponse(
                status_code=400,
                content={
                    "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                    "title": "OTP is invalid",
                },
            )

    change_notif: ChangeNotifResult = await notif_preference_service.change_notif(
        phone_number=profile["phone_number"],
        language=profile["language"].upper(),
        preferred_language=profile["preferred_language"],
        notification_preferences=notification_preferences,
    )

    if not change_notif.Success:
        return HTTPException(status_code=500)

    return {"messsage": "done"}


@router.post(
    "/otp/request", responses=notification_preferences.otp_request_sample_response
)
async def request_notification_preferences_otp(
    notification_preferences_otp: NotificationPreferencesOTP,
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    phone_number = profile["phone_number"]
    notification_preferences_otp = notification_preferences_otp.dict(exclude_unset=True)

    language = profile["language"]

    if "phone_number" in notification_preferences_otp:

        res = await notification_client.send_sms_otp(
            phone_number=phone_number,
            recipient=notification_preferences_otp["phone_number"],
            off_net=False,
            language=language,
            intention="options_sms_notification",
            client_id=profile["client_id"],
        )

    elif "email_address" in notification_preferences_otp:

        res = await notification_client.send_email_otp(
            recipient=phone_number,
            language=language,
            intention="options_email_notification",
            client_id=profile["client_id"],
            # channel="email",
            email_address=notification_preferences_otp["email_address"],
        )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))
    else:
        if "phone_number" in notification_preferences_otp:
            return {"phone_number": res["recipient"]}
        return Response(status_code=200)
