from pydantic import BaseModel,field_validator


class FavoriteNumberInput(BaseModel):
    b_number: str
    nick_name: str = ""

    @field_validator("b_number")
    def phone_number_valid(cls, v):
        if len(v) != 12 or not v.startswith("98"):
            raise ValueError("Phone number is invalid")
        return v


class DeleteFavoriteNumberInput(BaseModel):
    b_numbers: list[str]

    @field_validator("b_numbers")
    def phone_number_valid(cls, v):
        for phone_number in v:
            if len(phone_number) != 12 or not phone_number.startswith("98"):
                raise ValueError("Phone number is invalid")
        return v
