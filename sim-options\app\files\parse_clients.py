
def parse_clients(ELIGIBLE_CLIENTS):

    ELIGIBLE_CLIENT_IDS = [i["id"] for i in ELIGIBLE_CLIENTS["clients"]]
    ELIGIBLE_CLIENT_SECRET_ID_PAIR = {}
    ELIGIBLE_CLIENT_NAME_ID_PAIR = {}
    ELIGIBLE_CLIENT_DESCRIPTION_ID_PAIR = {}
    ELIGIBLE_CLIENT_OTP_HASH_ID_PAIR = {}

    for client in ELIGIBLE_CLIENTS["clients"]:
        ELIGIBLE_CLIENT_SECRET_ID_PAIR[client["id"]] = client.get("secret")

    for client in ELIGIBLE_CLIENTS["clients"]:
        EL<PERSON><PERSON>LE_CLIENT_NAME_ID_PAIR[client["id"]] = client["display_name"]
    
    for client in ELIGIBLE_CLIENTS["clients"]:
        ELIGIBLE_CLIENT_DESCRIPTION_ID_PAIR[client["id"]] = client["description"]
    
    for client in ELIGIBLE_CLIENTS["clients"]:
        ELIGIBLE_CLIENT_OTP_HASH_ID_PAIR[client["id"]] = client["otp_hash"]
    
    return {
        "ids_list": ELIGIBLE_CLIENT_IDS,
        "id_secret_pair": ELIGIBLE_CLIENT_SECRET_ID_PAIR,
        "id_name_pair": ELIGIBLE_CLIENT_NAME_ID_PAIR,
        "id_description": ELIGIBLE_CLIENT_DESCRIPTION_ID_PAIR,
        "id_otp_hash_pair": ELIGIBLE_CLIENT_OTP_HASH_ID_PAIR
    }