from ngmi_http import HTTPBase


class PushNotificationClient(HTTPBase):

    def __init__(self, base_url, logger, http_client=None) -> None:
        super().__init__(logger, http_client)
        self.base_url = base_url

    async def _call_api(
        self,
        url,
        method,
        body,
        api_name,
        subject,
        params=None,
        headers=None,
        timeout=None,
    ):
        response = await self._call_rest_api(
            url=url,
            method=method,
            body=body,
            headers=headers,
            params=params,
            api_name=api_name,
            service="notification_service",
            is_internal=True,
            subject=subject,
            timeout=timeout,
        )
        return response

    async def opt_out(self, phone_number):
        url = f"{self.base_url}/v1/push/opt_out"

        body = {
            "phone_number": phone_number,
        }

        res = {"error_status": False}

        response = await self._call_api(
            url=url,
            method="POST",
            body=body,
            api_name="opt_out",
            subject=phone_number,
            timeout=20,
        )

        if response["status_code"] != 200:
            res["error_status"] = True
        return res

    async def opt_in(self, phone_number, language):
        url = f"{self.base_url}/v1/push/opt_in"

        body = {"language": language, "phone_number": phone_number}

        res = {"error_status": False}

        response = await self._call_api(
            url=url,
            method="POST",
            body=body,
            api_name="opt_in",
            subject=phone_number,
            timeout=20,
        )

        if response["status_code"] != 200:
            res["error_status"] = True
        return res
