ARG CI_REGISTRY=docker.io

FROM $CI_REGISTRY/tiangolo/uvicorn-gunicorn-fastapi:python3.9

ARG CI_REGISTRY
ARG CI_REGISTRY_USER
ARG CI_REGISTRY_PASSWD

ARG REPOSITORY_PYPI=https://pypi.org/simple

RUN echo "machine ${CI_REGISTRY} login ${CI_REGISTRY_USER} password ${CI_REGISTRY_PASSWD}" > ~/.netrc
RUN chmod 0600 ~/.netrc

WORKDIR /app/


COPY ./requirements.txt /requirements.txt
RUN pip install --upgrade pip -i "${REPOSITORY_PYPI}"

# Install packages
RUN  head -c 5 /dev/random > random_bytes && pip install -i "${REPOSITORY_PYPI}" --no-cache-dir --upgrade -r /requirements.txt

COPY ./app /app

ENV PYTHONPATH=/app

EXPOSE 80

