def persian_text_normalizer(text):
    text = text.strip()
    unicode_map = {
        "\u064a": "\u06cc",  # yeh
        "\u0649": "\u06cc",  # yeh
        "\uFBFC": "\u06cc",  # yeh
        "\u0643": "\u06a9",  # keh
        "\u06aa": "\u06a9",  # keh
        "\u0623": "\u0627",  # <PERSON>ef hamza to alef
        "\u0622": "\u0627",  # Alef madda to alef
        "\u0625": "\u0627",  # Alef kasre to alef
        # arabic numbers
        "\u0660": "0",
        "\u0661": "1",
        "\u0662": "2",
        "\u0663": "3",
        "\u0664": "4",
        "\u0665": "5",
        "\u0666": "6",
        "\u0667": "7",
        "\u0668": "8",
        "\u0669": "9",
        # farsi numbers
        "\u06f0": "0",
        "\u06f1": "1",
        "\u06f2": "2",
        "\u06f3": "3",
        "\u06f4": "4",
        "\u06f5": "5",
        "\u06f6": "6",
        "\u06f7": "7",
        "\u06f8": "8",
        "\u06f9": "9",
        # eraaab
        "\u0651": "",  # tashdid
        "\u0652": "",  # sukon (gerd)
        "\u064b": "",  # fathatan
        "\u064f": "",  # oh
        "\u064e": "",  # fatha
        "\u0650": "",  # kasra
        # half spaces
        "\u200c": " ",
        "\u200e": " ",
        "\u200f": " ",
        "\u0654": " ",  # hamzeh to space
        "\u0655": " ",  # hamzeh to space
        # bad spaces
        "\xa0": " ",
        "\r": "",
    }

    # replace all unicode chars with their mapped ones
    normalized_text = "".join(unicode_map.get(char, char) for char in text)

    return normalized_text
