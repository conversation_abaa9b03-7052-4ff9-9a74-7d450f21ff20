from typing import Any, Annotated
from fastapi import APIRouter, Depends
from core.config import authorization, eia_client
from swagger import puk

from services import PukService
from core.config import get_puk_service


router = APIRouter()


@router.get("/puk", responses=puk.pull_sample_response)
async def puk(
    puk_service: Annotated[PukService, Depends(get_puk_service)],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:
    """Retrieve subscriber sim puks

    Raises:

        401: If JWT token is invalid

    Returns:

        puk_1: Represents subscriber sim puk 1
        puk_2: Represents subscriber sim puk 2

    """
    phone_number = profile["phone_number"]
    customer_type = profile["customer_type"]
    language = profile["language"]

    return await puk_service.account_details(
        phone_number=phone_number,
        customer_type=customer_type,
        language=language,
        fields=["puk_1", "puk_2"],
    )
