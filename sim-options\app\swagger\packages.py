packages_history_sample_response = {
    401: {
        "description": "Invalid JWT Token",
        "content": {
            "application/json": {"example": {"detail": "JWT token is invalid."}}
        },
    },
    200: {
        "description": "Packages history",
        "content": {
            "application/json": {
                "example": [
                    {
                        "name": "30 روزه ۶۰ گیگابایت",
                        "active_date": "2021-06-05 22:00:00",
                        "expiry_date": "2021-07-05 22:00:00",
                    },
                    {
                        "name": "30 روزه ۶۰ گیگابایت",
                        "active_date": "2021-06-05 22:00:00",
                        "expiry_date": "2021-07-05 22:00:00",
                    },
                    {
                        "name": "30 روزه ۶۰ گیگابایت",
                        "active_date": "2021-06-05 22:00:00",
                        "expiry_date": "2021-07-05 22:00:00",
                    },
                    {
                        "name": "30 روزه ۶۰ گیگابایت",
                        "active_date": "2021-06-05 22:00:00",
                        "expiry_date": "2021-07-05 22:00:00",
                    },
                    {
                        "name": "30 روزه ۶۰ گیگابایت",
                        "active_date": "2021-06-05 22:00:00",
                        "expiry_date": "2021-07-05 22:00:00",
                    },
                ]
            }
        },
    },
}
