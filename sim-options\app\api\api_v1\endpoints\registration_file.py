from typing import Any, Annotated
import time
from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import pytz
from io import BytesIO
import mimetypes
import jwt as pyjwt
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from fastapi import APIRouter, Depends, Request
from fastapi.responses import JSONResponse
from fastapi import Request
from fastapi.responses import StreamingResponse


from core.config import authorization, notification_client, dms_client, minio_client
from core.settings import settings

from schemas import RegistrationOtp


from utils import convert_base64_to_pdf

from services import RegistrationFileService
from core.config import get_registration_file_service

router = APIRouter()


@router.post("/registration/request")
async def registration_otp(
    request: Request, profile: Annotated[dict, Depends(authorization)]
):
    phone_number = profile["phone_number"]
    language = profile["language"]

    res = await notification_client.send_sms_otp(
        phone_number=phone_number,
        language=language,
        intention="registration_file",
        client_id=profile["client_id"],
        # sim_type=profile["sim_type"],
    )

    if res["error_status"]:
        return JSONResponse(status_code=400, content=res.get("data"))

    return {"recipient": res["recipient"]}


@router.post("/registration/download/token")
async def regist_download_token(
    regist_otp: RegistrationOtp,
    registration_file_servic: Annotated[
        RegistrationFileService, Depends(get_registration_file_service)
    ],
    profile: Annotated[dict, Depends(authorization)],
) -> Any:

    phone_number = profile["phone_number"]
    regist_otp = regist_otp.model_dump()

    res = await notification_client.validate_otp(
        recipient=regist_otp["recipient"],
        intention="registration_file",
        otp=regist_otp["otp"],
        originated_msisdn=profile["phone_number"],
    )

    if res["error_status"]:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/authorization/otp/invalid",
                "title": "OTP is invalid",
            },
        )

    response = await registration_file_servic.download_token(
        minio_client=minio_client,
        phone_number=profile["phone_number"],
        installation_id=profile["installation_id"],
    )

    if not "record" in response:
        return JSONResponse(
            status_code=400,
            content={
                "type": "https://my.irancell.ir/errors/registration_form/not_available",
                "title": "The registration file is not available",
                "detail": f"The phone number {phone_number} does not have active document",
                "params": {"phone_number": phone_number},
            },
        )

    return response


@router.get("/registration/file")
async def registration_form_download(
    token: str,
    registration_file_servic: Annotated[
        RegistrationFileService, Depends(get_registration_file_service)
    ],
) -> Any:

    try:
        token_decoded = jwt.decode(token, settings.JWT_PUBLIC_KEY, algorithms=["RS256"])
    except JWTError as e:
        return JSONResponse(status_code=400, content={"error": str(e)})

    response = await registration_file_servic.download_file(
        token_decoded=token_decoded,
        minio_client=minio_client,
    )

    if not response.file_exist:
        return JSONResponse(status_code=404, content="File Not Found")
