from typing import Any
from pymongo import <PERSON>goC<PERSON>
from ngmi_logging.utils import process_time
from .utils import dordaneh_data
from .utils import sim_upgrade_data
from .utils import termination_request_data


@process_time()
async def add_termination_request(termination_request: dict, db: MongoClient) -> dict:
    termination_request = await db.get_collection("termination_requests").insert_one(
        termination_request
    )
    new_termination_request = await db.get_collection("termination_requests").find_one(
        {"_id": termination_request.inserted_id}
    )
    if new_termination_request:
        return termination_request_data(new_termination_request)


@process_time()
async def get_termination_requests(query: dict, db: MongoClient) -> list:
    retrieved_requests = db.get_collection("termination_requests").find(query)
    requests = []
    async for req in retrieved_requests:
        requests.append(termination_request_data(req))
    return requests


@process_time()
async def count_termination_requests(query: dict, db: MongoClient) -> list:
    today_requests = await db.get_collection("termination_requests").count_documents(
        query
    )
    return today_requests


@process_time()
async def update_termination_request(query: dict, data: dict, db: MongoClient) -> Any:
    return await db.get_collection("termination_requests").update_one(query, data)


@process_time()
async def update_termination_requests(query: dict, data: dict, db: MongoClient) -> Any:
    await db.get_collection("termination_requests").update_many(query, data)
    return None


@process_time()
async def find_one_request(query: dict, db: MongoClient) -> dict:
    retrieved_request = await db.get_collection("termination_requests").find_one(query)
    if retrieved_request:
        return termination_request_data(retrieved_request)
    return {}


@process_time()
async def get_dordaneh(query: dict, db: MongoClient) -> dict:
    item = await db.get_collection("dordaneh").find_one(query)
    if item:
        return dordaneh_data(item)
    return {}


@process_time()
async def create_new_dordaneh(new_dordaneh: dict, db: MongoClient) -> dict:
    dordaneh = await db.get_collection("dordaneh").insert_one(new_dordaneh)
    new_dordaneh_field = await db.get_collection("dordaneh").find_one(
        {"_id": dordaneh.inserted_id}
    )
    if new_dordaneh_field:
        return dordaneh_data(new_dordaneh_field)


@process_time()
async def update_dordaneh(update_data: dict, db: MongoClient) -> Any:
    await db.get_collection("dordaneh").update_one(
        {
            "msisdn": update_data["msisdn"],
            "registration_date": update_data["registration_date"],
        },
        {"$push": {"dordaneh": update_data["dordaneh"]}},
    )
    return None


@process_time()
async def delete_dordaneh(delete_query: dict, db: MongoClient) -> Any:
    await db.get_collection("dordaneh").update_one(
        {
            "msisdn": delete_query["msisdn"],
            "registration_date": delete_query["registration_date"],
        },
        {"$pull": {"dordaneh": delete_query["dordaneh"]}},
    )

    item = await db.get_collection("dordaneh").find_one(
        {"msisdn": delete_query["msisdn"], "dordaneh": []}
    )
    if item:
        await db.get_collection("dordaneh").delete_one(
            {"msisdn": delete_query["msisdn"]}
        )
    return None


@process_time()
async def exist_in_dordaneh_db(query: dict, db: MongoClient) -> dict:
    item = await db.get_collection("dordaneh").find_one(
        {
            "$or": [
                {"msisdn": query["dordaneh"]},
                {"dordaneh": query["msisdn"]},
                {"dordaneh": query["dordaneh"]},
            ]
        }
    )
    if item:
        return True
    return False


@process_time()
async def remove_dordaneh_form_db(query: dict, db: MongoClient) -> dict:
    dordaneh = db.get_collection("dordaneh").find_one(query)
    if dordaneh:
        db.get_collection("dordaneh").update_one(dordaneh, {"$pull": query})
        return True
    return False


@process_time()
async def add_sim_upgrade_postal_info(data: dict, db: MongoClient) -> dict:
    postal_info = await db.get_collection("sim_upgrade").insert_one(data)
    new_postal_info = await db.get_collection("sim_upgrade").find_one(
        {"_id": postal_info.inserted_id}
    )
    return sim_upgrade_data(new_postal_info)


@process_time()
async def add_batch_id(end_date, status, batch_id, db: MongoClient) -> dict:
    query = {
        "request_datetime": {"$lte": end_date},
        "status": status,
    }

    update = {
        "$set": {"batch_id": batch_id},
    }
    result = await db.get_collection("termination_requests").update_many(query, update)
    modified_count = result.modified_count
    return {"updated_documents": modified_count}


@process_time()
async def remove_batch_id(batch_id, status, db: MongoClient) -> dict:
    query = {"batch_id": batch_id}
    update = {"$set": {"status": status}, "$unset": {"batch_id": 1}}
    result = await db.get_collection("termination_requests").update_many(query, update)
    modified_count = result.modified_count
    return {"updated_documents": modified_count}
