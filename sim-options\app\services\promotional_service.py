from datetime import datetime
from pydantic import BaseModel
from typing import Optional


class DndRemoveResult(BaseModel):
    error_status: bool = False
    promotional_sms: Optional[bool] = None


class PromotionalService:
    def __init__(
        self,
        ecp_client,
    ):
        self.ecp_client = ecp_client

    async def dnd_add_remove(self, phone_number, status):
        ecp_response = await self.ecp_client.add_remove_dnd(phone_number, status)

        if ecp_response["status_code"] in ["5121", "5123"]:
            return DndRemoveResult(error_status=True)

        elif ecp_response["status_code"] != "5000":
            return DndRemoveResult(error_status=True)

        return DndRemoveResult(promotional_sms=status)
