def aat_parser(headers=None, body=None):
    req_body_for_log = body.copy() if body else None
    req_header_for_log = headers.copy() if headers else None
    if req_body_for_log and req_body_for_log.get("password"):
        req_body_for_log["password"] = "****"

    if req_header_for_log and req_header_for_log.get("authorization"):
        req_header_for_log["authorization"] = "****"

    return req_header_for_log, req_body_for_log
