from enum import Enum
import uuid
from pydantic import BaseModel , field_validator , ValidationError


class StatusReport(str, Enum):
    in_progress = "in-progress",
    cancelled = "cancelled"

class TerminationRequset(BaseModel):
    termination_grace_period: int =  1
    status: StatusReport



class StatusDoneReport(str, Enum):
    reported = "reported"
    reported_cancelled = "reported-cancelled"


class RemoveBatchIDRequest(BaseModel):
    batch_id: str
    status: StatusDoneReport

    @field_validator("batch_id")
    def validation_uuid(cls, v):
        try:
            uuid_obj = uuid.UUID(v, version=4)
        except ValueError:
            raise ValidationError("batch_id must be a valid UUIDv")

        return str(uuid_obj)